(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{2383:()=>{},2657:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3686:()=>{},4957:(e,a,t)=>{"use strict";t.d(a,{U:()=>l});var r=t(5731);let l={getAll:async()=>(await r.u.get("/categories")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get("/categories/paginated?".concat(a))},async getById(e){try{return(await r.u.get("/categories/".concat(e))).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await r.u.post("/categories",e)},async update(e,a){await r.u.put("/categories/".concat(e),a)},async delete(e){await r.u.delete("/categories/".concat(e))}}},4975:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var r=t(5731);let l={getAll:async()=>(await r.u.get("/campaigns")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get("/campaigns/paginated?".concat(a))},async getById(e){try{return(await r.u.get("/campaigns/".concat(e))).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await r.u.post("/campaigns",e)},async update(e,a){await r.u.put("/campaigns/".concat(e),a)},async delete(e){await r.u.delete("/campaigns/".concat(e))},getActive:async()=>(await r.u.get("/campaigns/active")).data||[]}},5339:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5876:(e,a,t)=>{"use strict";t.d(a,{A:()=>i});var r=t(5155),l=t(2115),s=t(5695),n=t(283);function i(e){let{children:a,requireAdmin:t=!1}=e,{isAuthenticated:i,isAdmin:c,isLoading:d}=(0,n.A)(),o=(0,s.useRouter)();return((0,l.useEffect)(()=>{if(!d){if(!i)return void o.push("/login");if(t&&!c)return void o.push("/")}},[i,c,d,t,o]),d)?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):i&&(!t||c)?(0,r.jsx)(r.Fragment,{children:a}):null}},6413:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>K});var r=t(5155),l=t(2115),s=t(5695),n=t(5809),i=t(5876),c=t(283),d=t(6759),o=t(3741),u=t(6440),m=t(3915),x=t(542),p=t(7108),g=t(2657),h=t(3717),y=t(2525),j=t(9946);let v=(0,j.A)("package-plus",[["path",{d:"M16 16h6",key:"100bgy"}],["path",{d:"M19 13v6",key:"85cyf1"}],["path",{d:"M21 10V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l2-1.14",key:"e7tb2h"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["line",{x1:"12",x2:"12",y1:"22",y2:"12",key:"a4e8g8"}]]),f=(0,j.A)("file-spreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]]);var b=t(4616),k=t(7924),N=t(9604),w=t(4957),_=t(4975);let A=(0,j.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var S=t(5339);let C=(0,j.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);var E=t(3925);function z(e){let{isOpen:a,onClose:t,onSuccess:s}=e,[n,i]=(0,l.useState)(null),[c,d]=(0,l.useState)(20),[x,p]=(0,l.useState)([]),[g,h]=(0,l.useState)(!1),[y,j]=(0,l.useState)(null),[v,b]=(0,l.useState)("upload"),k=e=>{let a=new FileReader;a.onload=e=>{try{var a,t,r,l,s,n,i,c,d;let o=new Uint8Array(null==(a=e.target)?void 0:a.result),u=E.LF(o,{type:"array"}),m=u.SheetNames[0],x=u.Sheets[m],g=E.Wp.sheet_to_json(x,{header:1}),h=-1;for(let e=0;e<g.length;e++){let a=g[e];if(a&&a.length>0){for(let r=0;r<a.length;r++){let l=(null==(t=a[r])?void 0:t.toString().toLowerCase().trim())||"";if(console.log(l),l.includes("\xfcr\xfcn kodu")||l.includes("urun kodu")||"kod"===l){h=e;break}}if(-1!==h)break}}if(-1===h)return void alert('Excel dosyasında "\xdcr\xfcn Kodu" başlığı bulunamadı. L\xfctfen dosya formatını kontrol edin.');let y=g.slice(h+1).map(e=>e&&0!==e.length?[e[1]||"",e[2]||"",e[3]||"",e[4]||""]:[]),j=[],v="";for(let e=0;e<y.length;e++){let a=y[e];if(!a||a.length<2)continue;let t="",o=(null==(r=a[0])?void 0:r.toString().trim())||"";t=(null==(l=a[1])?void 0:l.toString().trim())===(null==(s=a[2])?void 0:s.toString().trim())?(null==(i=a[1])?void 0:i.toString().trim())||"":((null==(c=a[1])?void 0:c.toString().trim())||"")+((null==(d=a[2])?void 0:d.toString().trim())||"");let u=(null==(n=a[3])?void 0:n.toString().trim())||"";if(o||t){if(!o&&t){v=t;continue}if(o){let e=0;if(u){let a=u.replace(/[^\d,.-]/g,"").replace(",",".");e=parseFloat(a)||0}if(e>0&&t){let a={product_code:o,product_name:t,wholesale_price:e,category_name:v||"Genel"};j.push(a)}}}}p(j),b("preview")}catch(e){console.error("Error parsing Excel file:",e),alert("Excel dosyası okunurken hata oluştu. L\xfctfen dosya formatını kontrol edin.")}},a.readAsArrayBuffer(e)},w=async()=>{if(0===x.length)return void alert("İ\xe7e aktarılacak \xfcr\xfcn bulunamadı.");h(!0);try{let e=await N.j.importFromExcel({products:x,profit_margin:c});j(e),b("result")}catch(e){console.error("Error importing products:",e),alert("\xdcr\xfcnler i\xe7e aktarılırken hata oluştu: "+e.message)}finally{h(!1)}},_=()=>{i(null),p([]),j(null),b("upload"),d(20),t()};return(0,r.jsx)(u.A,{isOpen:a,onClose:_,title:"Excel ile \xdcr\xfcn Y\xfckleme",children:(0,r.jsxs)("div",{className:"space-y-6",children:["upload"===v&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center",children:[(0,r.jsx)(f,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Excel dosyanızı se\xe7in veya s\xfcr\xfckleyip bırakın"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Desteklenen formatlar: .xlsx, .xls"})]}),(0,r.jsx)("input",{type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let t=null==(a=e.target.files)?void 0:a[0];console.log(n),t&&(i(t),k(t))},className:"hidden",id:"excel-file"}),(0,r.jsxs)("label",{htmlFor:"excel-file",className:"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer",children:[(0,r.jsx)(A,{className:"w-4 h-4 mr-2"}),"Dosya Se\xe7"]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Excel Dosya Formatı:"}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"A S\xfctunu:"})," \xdcr\xfcn Kodu"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"B S\xfctunu:"})," \xdcr\xfcn Adı"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"C S\xfctunu:"})," NAKİT TOPTAN \xd6deme (Fiyat)"]}),(0,r.jsx)("li",{children:"• \xdcr\xfcn kodu boş olan satırlar kategori adı olarak kabul edilir"}),(0,r.jsx)("li",{children:"• \xdcr\xfcnler varsayılan olarak 0 stok ile kaydedilir"})]})]})]}),"preview"===v&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:"\xd6nizleme"}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[x.length," \xfcr\xfcn bulundu"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Kar Oranı (%)"}),(0,r.jsx)(m.A,{type:"number",value:c,onChange:e=>d(Number(e.target.value)),placeholder:"Kar oranını girin",min:"0",step:"0.1"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Toptan fiyat \xfczerine eklenecek kar oranı"})]}),(0,r.jsxs)("div",{className:"max-h-60 overflow-y-auto border rounded-lg",children:[(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"\xdcr\xfcn Kodu"}),(0,r.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"\xdcr\xfcn Adı"}),(0,r.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Kategori"}),(0,r.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Toptan Fiyat"}),(0,r.jsx)("th",{className:"px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase",children:"Satış Fiyatı"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:x.slice(0,10).map((e,a)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900",children:e.product_code}),(0,r.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900",children:e.product_name}),(0,r.jsx)("td",{className:"px-3 py-2 text-sm text-gray-900",children:e.category_name}),(0,r.jsxs)("td",{className:"px-3 py-2 text-sm text-gray-900",children:[e.wholesale_price.toFixed(2)," TL"]}),(0,r.jsxs)("td",{className:"px-3 py-2 text-sm text-gray-900",children:[(e.wholesale_price*(1+c/100)).toFixed(2)," ","TL"]})]},a))})]}),x.length>10&&(0,r.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 text-center bg-gray-50",children:["... ve ",x.length-10," \xfcr\xfcn daha"]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)(o.A,{variant:"secondary",onClick:()=>b("upload"),className:"flex-1",children:"Geri"}),(0,r.jsx)(o.A,{onClick:w,disabled:g,className:"flex-1",children:g?"İ\xe7e Aktarılıyor...":"İ\xe7e Aktar"})]})]}),"result"===v&&y&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"text-center space-y-4",children:[y.errors&&y.errors.length>0?(0,r.jsx)(S.A,{className:"mx-auto h-12 w-12 text-yellow-500"}):(0,r.jsx)(C,{className:"mx-auto h-12 w-12 text-green-500"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"İ\xe7e Aktarma Tamamlandı"}),(0,r.jsxs)("div",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:["Toplam \xfcr\xfcn: ",y.total_products]}),(0,r.jsxs)("p",{children:["Yeni oluşturulan \xfcr\xfcn: ",y.created_products]}),(0,r.jsxs)("p",{children:["G\xfcncellenen \xfcr\xfcn: ",y.updated_products]}),(0,r.jsxs)("p",{children:["Oluşturulan kategori: ",y.created_categories]})]})]}),y.errors&&y.errors.length>0&&(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg text-left",children:[(0,r.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:"Uyarılar:"}),(0,r.jsx)("ul",{className:"text-sm text-yellow-700 space-y-1",children:y.errors.map((e,a)=>(0,r.jsxs)("li",{children:["• ",e]},a))})]})]}),(0,r.jsx)(o.A,{onClick:()=>{_(),s()},className:"w-full",children:"Tamam"})]})]})})}function q(e){let{isOpen:a,onClose:t,onSuccess:s}=e,[n,i]=(0,l.useState)(""),[c,d]=(0,l.useState)([]),[x,g]=(0,l.useState)(null),[h,y]=(0,l.useState)(0),[j,v]=(0,l.useState)(!1),[f,w]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=setTimeout(async()=>{if(n.trim().length<2)return void d([]);w(!0);try{let e=await N.j.search(n);d(e)}catch(e){console.error("Error searching products:",e),d([])}finally{w(!1)}},300);return()=>clearTimeout(e)},[n]);let _=e=>{g(e),i(e.name),d([])},A=async()=>{if(!x||h<=0)return void alert("L\xfctfen \xfcr\xfcn se\xe7in ve ge\xe7erli bir miktar girin.");v(!0);try{let e=x.quantity+h;await N.j.update(x.id,{quantity:e}),S(),s()}catch(e){console.error("Error adding stock:",e),alert("Stok eklenirken hata oluştu: "+e.message)}finally{v(!1)}},S=()=>{i(""),d([]),g(null),y(0),t()};return(0,r.jsx)(u.A,{isOpen:a,onClose:S,title:"Stok Ekle",size:"lg",children:(0,r.jsxs)("div",{className:"space-y-4 min-h-[400px]",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Ara"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(k.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(m.A,{type:"text",value:n,onChange:e=>i(e.target.value),placeholder:"\xdcr\xfcn adı veya kodu ile ara...",className:"pl-10"})]}),c.length>0&&(0,r.jsx)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl max-h-80 overflow-y-auto",children:c.map(e=>(0,r.jsx)("button",{onClick:()=>_(e),className:"w-full px-4 py-4 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 min-h-[80px]",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-blue-600 mr-3 mt-1 flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900 leading-5 mb-1 break-words",children:e.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 leading-4",children:["Kod: ",e.product_code," | Mevcut Stok: ",e.quantity]})]})]})},e.id))}),f&&(0,r.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-4 text-center",children:(0,r.jsx)("div",{className:"text-gray-500",children:"Aranıyor..."})})]}),x&&(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Se\xe7ilen \xdcr\xfcn"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm text-blue-800",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"\xdcr\xfcn:"})," ",x.name]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Kod:"})," ",x.product_code]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Mevcut Stok:"})," ",x.quantity," adet"]})]})]}),x&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Eklenecek Miktar"}),(0,r.jsx)(m.A,{type:"number",value:h,onChange:e=>y(parseInt(e.target.value)||0),placeholder:"Eklenecek adet sayısı",min:"1"}),h>0&&(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Yeni stok miktarı: ",(0,r.jsxs)("strong",{children:[x.quantity+h," adet"]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(o.A,{variant:"secondary",onClick:S,children:"İptal"}),(0,r.jsxs)(o.A,{onClick:A,disabled:!x||h<=0||j,children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),j?"Ekleniyor...":"Stok Ekle"]})]})]})})}function K(){let e=(0,s.useRouter)(),{user:a}=(0,c.A)(),t=(0,d.jp)(),[j,A]=(0,l.useState)([]),[S,C]=(0,l.useState)([]),[E,K]=(0,l.useState)([]),[F,M]=(0,l.useState)(!0),[T,O]=(0,l.useState)(!1),[P,L]=(0,l.useState)(!1),[B,U]=(0,l.useState)(!1),[D,I]=(0,l.useState)(!1),[R,Y]=(0,l.useState)(null),[G,H]=(0,l.useState)({name:"",product_code:"",price:0,quantity:0,category_id:"",campaign_id:"",organization_id:""}),[Z,V]=(0,l.useState)(""),[W,J]=(0,l.useState)(""),[Q,X]=(0,l.useState)("all"),[$,ee]=(0,l.useState)("all"),[ea]=(0,l.useState)(5),[et,er]=(0,l.useState)({page:1,per_page:10});(0,l.useEffect)(()=>{el()},[]);let el=async()=>{try{M(!0);let[e,a,t]=await Promise.all([N.j.getAll(),w.U.getAll(),_.A.getAll()]);A(e),C(a),K(t)}catch(e){console.error("Error loading data:",e)}finally{M(!1)}},es=()=>{V(W),er(e=>({...e,page:1}))},en=e=>{J(e)},ei=e=>{X(e),er(e=>({...e,page:1}))},ec=e=>{ee(e),er(e=>({...e,page:1}))},ed=()=>{V(""),J(""),X("all"),ee("all"),er(e=>({...e,page:1}))},eo=async()=>{try{let e={...G,organization_id:t||(null==a?void 0:a.organization_id)||""};await N.j.create(e),O(!1),H({name:"",product_code:"",price:0,quantity:0,category_id:"",campaign_id:"",organization_id:""}),el()}catch(e){console.error("Error creating product:",e)}},eu=async()=>{if(R)try{let e={name:G.name,product_code:G.product_code,price:G.price,quantity:G.quantity,category_id:G.category_id,campaign_id:G.campaign_id};await N.j.update(R.id,e),L(!1),Y(null),el()}catch(e){console.error("Error updating product:",e)}},em=async e=>{if(confirm("Bu \xfcr\xfcn\xfc silmek istediğinizden emin misiniz?"))try{await N.j.delete(e),el()}catch(e){console.error("Error deleting product:",e)}},ex=e=>{Y(e),H({name:e.name,product_code:e.product_code,price:e.price,quantity:e.quantity,category_id:e.category_id,campaign_id:e.campaign_id,organization_id:t||(null==a?void 0:a.organization_id)||""}),L(!0)},ep=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),eg=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),eh=j.filter(e=>{let a=eg(Z),t=eg(e.name),r=eg(e.product_code),l=t.includes(a)||r.includes(a),s=!0;"in_stock"===Q?s=e.quantity>0:"low"===Q?s=e.quantity>0&&e.quantity<=ea:"out"===Q&&(s=0===e.quantity);let n="all"===$||e.category_id.toString()===$;return l&&s&&n}),ey=e=>0===e?{status:"out",color:"text-red-600",bgColor:"bg-red-100",text:"T\xfckendi"}:e<=ea?{status:"low",color:"text-yellow-600",bgColor:"bg-yellow-100",text:"D\xfcş\xfck Stok"}:{status:"good",color:"text-green-600",bgColor:"bg-green-100",text:"Stokta"},ej=[{key:"product_code",header:"Kod",className:"w-24",render:e=>(0,r.jsx)("div",{className:"font-medium text-gray-900 text-sm",children:e.product_code})},{key:"name",header:"\xdcr\xfcn Adı",className:"w-48",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-blue-600 mr-2 flex-shrink-0"}),(0,r.jsx)("div",{className:"font-medium text-gray-900 truncate",title:e.name,children:e.name.length>25?"".concat(e.name.substring(0,25),"..."):e.name})]})},{key:"category",header:"Kategori",className:"w-32",render:e=>{let a=S.find(a=>a.id.toString()===e.category_id.toString()),t=(null==a?void 0:a.name)||"Bilinmeyen";return(0,r.jsx)("div",{className:"text-sm text-gray-700 truncate",title:t,children:t.length>15?"".concat(t.substring(0,15),"..."):t})}},{key:"price",header:"Fiyat",className:"w-28",render:e=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:ep(e.price)}),e.discounted_price&&e.discounted_price!==e.price&&(0,r.jsx)("div",{className:"text-xs text-green-600",children:ep(e.discounted_price)})]})},{key:"quantity",header:"Stok",className:"w-24",render:e=>{let a=ey(e.quantity);return(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"font-medium text-sm mr-1",children:e.quantity}),(0,r.jsx)("span",{className:"px-1 py-0.5 rounded text-xs ".concat(a.bgColor," ").concat(a.color),children:a.text})]})}},{key:"campaign",header:"Kampanya",className:"w-20",render:e=>{if(e.campaign_id&&"0"!==e.campaign_id){let a=E.find(a=>a.id.toString()===e.campaign_id.toString()),t=(null==a?void 0:a.name)||"Bulunamadı";return(0,r.jsx)("span",{className:"text-green-600 text-xs truncate block",title:t,children:t.length>8?"".concat(t.substring(0,8),"..."):t})}return(0,r.jsx)("span",{className:"text-gray-400 text-xs",children:"-"})}},{key:"actions",header:"İşlemler",className:"text-right w-32",render:a=>(0,r.jsxs)("div",{className:"flex justify-end space-x-1",children:[(0,r.jsx)(o.A,{size:"sm",variant:"success",onClick:()=>e.push("/products/".concat(a.id)),title:"Detay",children:(0,r.jsx)(g.A,{className:"h-3 w-3"})}),(0,r.jsx)(o.A,{size:"sm",variant:"info",onClick:()=>ex(a),title:"D\xfczenle",children:(0,r.jsx)(h.A,{className:"h-3 w-3"})}),(0,r.jsx)(o.A,{size:"sm",variant:"danger",onClick:()=>em(a.id),title:"Sil",children:(0,r.jsx)(y.A,{className:"h-3 w-3"})})]})}];return F?(0,r.jsx)(i.A,{children:(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):(0,r.jsx)(i.A,{children:(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"\xdcr\xfcnler"}),(0,r.jsx)("p",{className:"text-gray-600",children:"\xdcr\xfcn stokunuzu y\xf6netin"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(o.A,{variant:"secondary",onClick:()=>I(!0),children:[(0,r.jsx)(v,{className:"h-4 w-4 mr-2"}),"Stok Ekle"]}),(0,r.jsxs)(o.A,{variant:"secondary",onClick:()=>U(!0),children:[(0,r.jsx)(f,{className:"h-4 w-4 mr-2"}),"Excel ile Y\xfckle"]}),(0,r.jsxs)(o.A,{onClick:()=>{if(0===S.length){alert("\xdcr\xfcn oluşturmadan \xf6nce en az bir kategori oluşturmanız gerekiyor. L\xfctfen \xf6nce Kategoriler sayfasından kategori ekleyin."),e.push("/categories");return}O(!0)},children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Yeni \xdcr\xfcn"]})]})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)("div",{className:"md:col-span-2",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(k.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"\xdcr\xfcn adı veya kod ara...",value:W,onChange:e=>en(e.target.value),onKeyPress:e=>"Enter"===e.key&&es(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsx)(o.A,{onClick:es,variant:"primary",className:"px-4 py-2",children:"Ara"}),(Z||"all"!==Q||"all"!==$)&&(0,r.jsx)(o.A,{onClick:ed,variant:"secondary",className:"px-4 py-2",children:"Temizle"})]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("select",{value:Q,onChange:e=>ei(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"T\xfcm Stoklar"}),(0,r.jsx)("option",{value:"in_stock",children:"Sadece Stokta Olanlar"}),(0,r.jsx)("option",{value:"low",children:"D\xfcş\xfck Stok"}),(0,r.jsx)("option",{value:"out",children:"T\xfckenen"})]})}),(0,r.jsx)("div",{children:(0,r.jsxs)("select",{value:$,onChange:e=>ec(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"all",children:"T\xfcm Kategoriler"}),S.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-between text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:[eh.length," \xfcr\xfcn g\xf6steriliyor",Z&&' • "'.concat(Z,'" araması'),"all"!==Q&&" • ".concat("in_stock"===Q?"Sadece stokta olanlar":"low"===Q?"D\xfcş\xfck stok":"T\xfckenen"," filtresi"),"all"!==$&&" • ".concat((e=>{let a=S.find(a=>a.id===e.toString());return(null==a?void 0:a.name)||"Bilinmeyen"})($)," kategorisi")]}),(Z||"all"!==Q||"all"!==$)&&(0,r.jsx)("button",{onClick:ed,className:"text-blue-600 hover:text-blue-800",children:"Filtreleri Temizle"})]})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)(x.A,{columns:ej,data:eh,pagination:{page:et.page,per_page:et.per_page,total:eh.length,total_pages:Math.ceil(eh.length/et.per_page),has_next:et.page<Math.ceil(eh.length/et.per_page),has_prev:et.page>1},onPageChange:e=>{er(a=>({...a,page:e}))},onPerPageChange:e=>{er({page:1,per_page:e})},loading:F,emptyMessage:"Hen\xfcz \xfcr\xfcn kaydı bulunmuyor",emptyIcon:(0,r.jsx)(p.A,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0,className:"min-w-full"})}),(0,r.jsx)(u.A,{isOpen:T,onClose:()=>O(!1),title:"Yeni \xdcr\xfcn Ekle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(m.A,{label:"\xdcr\xfcn Adı",value:G.name,onChange:e=>H({...G,name:e.target.value}),placeholder:"\xdcr\xfcn adını girin"}),(0,r.jsx)(m.A,{label:"\xdcr\xfcn Kodu",value:G.product_code,onChange:e=>H({...G,product_code:e.target.value}),placeholder:"\xdcr\xfcn kodu (\xf6rn: SAM-BUZ-001)"}),(0,r.jsx)(m.A,{label:"Fiyat",type:"number",value:G.price,onChange:e=>H({...G,price:parseFloat(e.target.value)||0}),placeholder:"0.00"}),(0,r.jsx)(m.A,{label:"Stok Miktarı",type:"number",value:G.quantity,onChange:e=>H({...G,quantity:parseInt(e.target.value)||0}),placeholder:"0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Kategori"}),(0,r.jsxs)("select",{value:G.category_id,onChange:e=>H({...G,category_id:e.target.value}),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:"Kategori se\xe7in"}),S.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Kampanya (Opsiyonel)"}),(0,r.jsxs)("select",{value:G.campaign_id,onChange:e=>H({...G,campaign_id:e.target.value}),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Kampanya yok"}),E.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(o.A,{variant:"secondary",onClick:()=>O(!1),children:"İptal"}),(0,r.jsx)(o.A,{onClick:eo,children:"\xdcr\xfcn Ekle"})]})]})}),(0,r.jsx)(u.A,{isOpen:P,onClose:()=>L(!1),title:"\xdcr\xfcn D\xfczenle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(m.A,{label:"\xdcr\xfcn Adı",value:G.name,onChange:e=>H({...G,name:e.target.value}),placeholder:"\xdcr\xfcn adını girin"}),(0,r.jsx)(m.A,{label:"\xdcr\xfcn Kodu",value:G.product_code,onChange:e=>H({...G,product_code:e.target.value}),placeholder:"\xdcr\xfcn kodu (\xf6rn: SAM-BUZ-001)"}),(0,r.jsx)(m.A,{label:"Fiyat",type:"number",value:G.price,onChange:e=>H({...G,price:parseFloat(e.target.value)||0}),placeholder:"0.00"}),(0,r.jsx)(m.A,{label:"Stok Miktarı",type:"number",value:G.quantity,onChange:e=>H({...G,quantity:parseInt(e.target.value)||0}),placeholder:"0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Kategori"}),(0,r.jsxs)("select",{value:G.category_id,onChange:e=>H({...G,category_id:e.target.value}),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"0",children:"Kategori se\xe7in"}),S.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Kampanya (Opsiyonel)"}),(0,r.jsxs)("select",{value:G.campaign_id,onChange:e=>H({...G,campaign_id:e.target.value}),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"0",children:"Kampanya yok"}),E.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(o.A,{variant:"secondary",onClick:()=>L(!1),children:"İptal"}),(0,r.jsx)(o.A,{onClick:eu,children:"G\xfcncelle"})]})]})}),(0,r.jsx)(q,{isOpen:D,onClose:()=>I(!1),onSuccess:el}),(0,r.jsx)(z,{isOpen:B,onClose:()=>U(!1),onSuccess:el})]})})})}},9604:(e,a,t)=>{"use strict";t.d(a,{j:()=>l});var r=t(5731);let l={getAll:async()=>(await r.u.get("/products")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get("/products/paginated?".concat(a))},async getById(e){try{return(await r.u.get("/products/".concat(e))).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let a={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await r.u.post("/products",a)},async update(e,a){let t={...a,campaign_id:a.campaign_id||"00000000-0000-0000-0000-000000000000"};await r.u.put("/products/".concat(e),t)},async delete(e){await r.u.delete("/products/".concat(e))},search:async e=>(await r.u.get("/products/search?q=".concat(encodeURIComponent(e)))).data||[],importFromExcel:async e=>(await r.u.post("/products/import-excel",e)).data}},9989:(e,a,t)=>{Promise.resolve().then(t.bind(t,6413))}},e=>{var a=a=>e(e.s=a);e.O(0,[524,122,269,809,564,441,684,977],()=>a(9989)),_N_E=e.O()}]);