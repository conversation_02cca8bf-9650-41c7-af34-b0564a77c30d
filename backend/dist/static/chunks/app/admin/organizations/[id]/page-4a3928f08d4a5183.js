(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{1567:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>y});var s=t(5155),r=t(2115),i=t(5695),n=t(2752),l=t(5876),c=t(3741),d=t(3915),o=t(6440),m=t(2525),x=t(7550),g=t(4616),h=t(7580),u=t(6488);function y(){let e=(0,i.useRouter)(),a=(0,i.useParams)().id,[t,y]=(0,r.useState)(null),[p,j]=(0,r.useState)([]),[b,v]=(0,r.useState)(!0),[f,N]=(0,r.useState)(!1),[z,w]=(0,r.useState)({name:"",description:""}),A=(0,r.useCallback)(async()=>{try{v(!0);let e=await u.h.getOrganizationById(a);y(e)}catch(e){console.error("Error loading organization details:",e)}finally{v(!1)}},[a]),k=(0,r.useCallback)(async()=>{try{let e=await u.h.getSubOrganizations(a);j(e)}catch(e){console.error("Error loading sub-organizations:",e)}},[a]);(0,r.useEffect)(()=>{a&&(A(),k())},[a,A,k]);let _=async()=>{try{await u.h.createSubOrganization(a,{...z,is_main:!1,main_org_id:a}),N(!1),w({name:"",description:""}),k()}catch(e){console.error("Error creating sub-organization:",e)}},O=async e=>{if(window.confirm("Bu alt organizasyonu silmek istediğinizden emin misiniz?"))try{await u.h.deleteOrganization(e),k()}catch(e){console.error("Error deleting sub-organization:",e)}},C=[{key:"name",header:"Organizasyon Adı",render:e=>(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name})},{key:"description",header:"A\xe7ıklama",render:e=>(0,s.jsx)("div",{className:"text-gray-600",children:e.description||"-"})},{key:"is_active",header:"Durum",render:e=>(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Aktif":"Pasif"})},{key:"created_at",header:"Oluşturulma Tarihi",render:e=>(0,s.jsx)("div",{className:"text-gray-600",children:e.created_at})},{key:"actions",header:"İşlemler",render:e=>(0,s.jsx)("div",{className:"flex items-center space-x-2 justify-end",children:(0,s.jsx)(c.A,{variant:"danger",size:"sm",onClick:()=>O(e.id),children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})})}];return b?(0,s.jsx)(l.A,{requireAdmin:!0,children:(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):t?(0,s.jsx)(l.A,{requireAdmin:!0,children:(0,s.jsx)(n.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(c.A,{variant:"secondary",onClick:()=>e.back(),children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:t.name}),(0,s.jsx)("p",{className:"text-gray-600",children:"Organizasyon Detayları"})]})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Organizasyon Bilgileri"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Organizasyon Adı"}),(0,s.jsx)("div",{className:"text-gray-900",children:t.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"A\xe7ıklama"}),(0,s.jsx)("div",{className:"text-gray-900",children:t.description||"-"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Durum"}),(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(t.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:t.is_active?"Aktif":"Pasif"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tip"}),(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(t.is_main?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"),children:t.is_main?"Ana Organizasyon":"Alt Organizasyon"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Oluşturulma Tarihi"}),(0,s.jsx)("div",{className:"text-gray-900",children:t.created_at})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"G\xfcncellenme Tarihi"}),(0,s.jsx)("div",{className:"text-gray-900",children:t.updated_at})]})]})]}),t.is_main&&(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border",children:[(0,s.jsx)("div",{className:"p-6 border-b",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Alt Organizasyonlar"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Bu organizasyona bağlı alt organizasyonlar"})]}),(0,s.jsxs)(c.A,{onClick:()=>N(!0),children:[(0,s.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Alt Organizasyon Ekle"]})]})}),(0,s.jsx)("div",{className:"p-6",children:p.length>0?(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsx)("tr",{children:C.map(e=>(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e.header},e.key))})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,s.jsx)("tr",{children:C.map(a=>(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.render?a.render(e):String(e[a.key]||"")},a.key))},e.id))})]})}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(h.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz alt organizasyon bulunmuyor"})]})})]}),(0,s.jsx)(o.A,{isOpen:f,onClose:()=>N(!1),title:"Alt Organizasyon Ekle",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(d.A,{label:"Organizasyon Adı",value:z.name,onChange:e=>w(a=>({...a,name:e.target.value})),placeholder:"Alt organizasyon adını girin",required:!0}),(0,s.jsx)(d.A,{label:"A\xe7ıklama",value:z.description,onChange:e=>w(a=>({...a,description:e.target.value})),placeholder:"A\xe7ıklama girin (opsiyonel)"}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,s.jsx)(c.A,{variant:"secondary",onClick:()=>N(!1),children:"İptal"}),(0,s.jsx)(c.A,{onClick:_,disabled:!z.name.trim(),children:"Alt Organizasyon Ekle"})]})]})})]})})}):(0,s.jsx)(l.A,{requireAdmin:!0,children:(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg text-red-600",children:"Organizasyon bulunamadı"})})})})}},3052:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3915:(e,a,t)=>{"use strict";t.d(a,{A:()=>l});var s=t(5155),r=t(2115),i=t(2596);let n=(0,r.forwardRef)((e,a)=>{let{className:t,label:r,error:n,helperText:l,type:c="text",...d}=e;return(0,s.jsxs)("div",{className:"w-full",children:[r&&(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:r}),(0,s.jsx)("input",{type:c,className:(0,i.A)("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",n&&"border-red-300 focus:ring-red-500 focus:border-red-500",t),ref:a,...d}),n&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n}),l&&!n&&(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:l})]})});n.displayName="Input";let l=n},6440:(e,a,t)=>{"use strict";t.d(a,{A:()=>d});var s=t(5155),r=t(2115),i=t(5939),n=t(280),l=t(4416),c=t(3741);function d(e){let{isOpen:a,onClose:t,title:d,children:o,size:m="md"}=e;return(0,s.jsx)(i.e,{appear:!0,show:a,as:r.Fragment,children:(0,s.jsxs)(n.lG,{as:"div",className:"relative z-50",onClose:t,children:[(0,s.jsx)(i.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,s.jsx)(i.e.Child,{as:r.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,s.jsxs)(n.lG.Panel,{className:"w-full ".concat({sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[m]," transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all"),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,s.jsx)(n.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:d}),(0,s.jsx)(c.A,{variant:"secondary",size:"sm",onClick:t,className:"p-2",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"px-6 py-4",children:o})]})})})})]})})}},6488:(e,a,t)=>{"use strict";t.d(a,{h:()=>r});var s=t(5731);let r={async createOrganization(e){await s.u.post("/organizations",e)},async getAllOrganizations(){try{let e=await s.u.get("/organizations");return(null==e?void 0:e.data)||[]}catch(e){return console.error("Error fetching organizations:",e),[]}},async getOrganizationsPaginated(e){try{let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()}),t=await s.u.get("/organizations/paginated?".concat(a));return(null==t?void 0:t.data)||{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}catch(e){return console.error("Error fetching paginated organizations:",e),{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}},async getOrganizationById(e){try{let a=await s.u.get("/organizations/".concat(e));return null==a?void 0:a.data}catch(e){throw console.error("Error fetching organization by id:",e),e}},async updateOrganization(e,a){await s.u.put("/organizations/".concat(e),a)},async deleteOrganization(e){await s.u.delete("/organizations/".concat(e))},async createSubOrganization(e,a){await s.u.post("/organizations/".concat(e,"/sub-organizations"),a)},async getSubOrganizations(e){try{let a=await s.u.get("/organizations/".concat(e,"/sub-organizations"));return(null==a?void 0:a.data)||[]}catch(e){return console.error("Error fetching sub organizations:",e),[]}},async getMainOrganizations(){try{let e=await s.u.get("/organizations/main");return(null==e?void 0:e.data)||[]}catch(e){return console.error("Error fetching main organizations:",e),[]}}}},7550:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8561:(e,a,t)=>{Promise.resolve().then(t.bind(t,1567))}},e=>{var a=a=>e(e.s=a);e.O(0,[122,269,329,441,684,977],()=>a(8561)),_N_E=e.O()}]);