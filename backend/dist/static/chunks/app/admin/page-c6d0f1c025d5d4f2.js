(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{3052:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},5598:(e,s,t)=>{Promise.resolve().then(t.bind(t,8237))},7703:(e,s,t)=>{"use strict";t.d(s,{Wu:()=>c,Zp:()=>i,aR:()=>d});var a=t(5155),l=t(2115),r=t(2596);let i=(0,l.forwardRef)((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.A)("bg-white rounded-lg border border-gray-200 shadow-sm",t),...i,children:l})});i.displayName="Card";let d=(0,l.forwardRef)((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.A)("px-6 py-4 border-b border-gray-200",t),...i,children:l})});d.displayName="CardHeader",(0,l.forwardRef)((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsx)("h3",{ref:s,className:(0,r.A)("text-lg font-semibold text-gray-900",t),...i,children:l})}).displayName="CardTitle",(0,l.forwardRef)((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsx)("p",{ref:s,className:(0,r.A)("text-sm text-gray-600 mt-1",t),...i,children:l})}).displayName="CardDescription";let c=(0,l.forwardRef)((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.A)("px-6 py-4",t),...i,children:l})});c.displayName="CardContent",(0,l.forwardRef)((e,s)=>{let{className:t,children:l,...i}=e;return(0,a.jsx)("div",{ref:s,className:(0,r.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",t),...i,children:l})}).displayName="CardFooter"},8237:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(5155),l=t(2115),r=t(2752),i=t(5876),d=t(7703),c=t(7580),n=t(9397),x=t(5525);let m=(0,t(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);var h=t(9959);function p(){let[e,s]=(0,l.useState)(!0),[t,p]=(0,l.useState)({totalUsers:0,activeUsers:0,adminUsers:0,regularUsers:0}),[o,u]=(0,l.useState)([]);(0,l.useEffect)(()=>{j()},[]);let j=async()=>{try{s(!0);let e=await h.y.getAllUsers(),t=e.length,a=e.filter(e=>e.is_active).length,l=e.filter(e=>"admin"===e.role).length,r=e.filter(e=>"user"===e.role).length;p({totalUsers:t,activeUsers:a,adminUsers:l,regularUsers:r}),u(e.slice(0,5))}catch(e){console.error("Error loading dashboard data:",e)}finally{s(!1)}};return e?(0,a.jsx)(i.A,{requireAdmin:!0,children:(0,a.jsx)(r.A,{children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):(0,a.jsx)(i.A,{requireAdmin:!0,children:(0,a.jsx)(r.A,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Sistem y\xf6netimi ve kullanıcı istatistikleri"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam Kullanıcı"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.totalUsers})]})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(n.A,{className:"h-8 w-8 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Aktif Kullanıcı"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.activeUsers})]})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-purple-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Admin"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.adminUsers})]})]})})}),(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(m,{className:"h-8 w-8 text-orange-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Normal Kullanıcı"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:t.regularUsers})]})]})})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son Kullanıcılar"})}),(0,a.jsx)(d.Wu,{children:0===o.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Hen\xfcz kullanıcı bulunmuyor"}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kullanıcı Adı"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rol"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Oluşturulma Tarihi"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.username}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("admin"===e.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"),children:"admin"===e.role?"Admin":"Kullanıcı"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_active?"Aktif":"Pasif"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.created_at})]},e.id))})]})})})]})]})})})}},9397:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[122,329,441,684,977],()=>s(5598)),_N_E=e.O()}]);