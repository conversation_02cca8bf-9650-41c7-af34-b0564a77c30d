(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[363],{2041:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>N});var t=s(5155),l=s(2115),r=s(5695),n=s(7559),i=s(3741),d=s(2814),c=s(7634),m=s(9434),x=s(1586),o=s(3717),u=s(2525),h=s(7580),g=s(9420),j=s(5868),p=s(9074),y=s(5339),b=s(6308);function N(){let e=(0,r.useParams)().id,[a,s]=(0,l.useState)(null),[N,v]=(0,l.useState)(!0),[f,w]=(0,l.useState)(null),k=(0,l.useCallback)(async()=>{try{v(!0),w(null);let a=await c.J.getById(e);if(!a)return void w("Bor\xe7 bulunamadı.");s(a)}catch(e){console.error("Error loading debt:",e),w("Bor\xe7 bilgileri y\xfcklenirken bir hata oluştu.")}finally{v(!1)}},[e]);(0,l.useEffect)(()=>{k()},[k]);let B=()=>a?"".concat(a.name," ").concat(a.surname).trim():"",_=a&&(0,t.jsxs)(t.Fragment,{children:[!a.is_paid&&(0,t.jsxs)(i.A,{variant:"warning",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"\xd6deme Yap"]}),(0,t.jsxs)(i.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,t.jsxs)(i.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,t.jsx)(n.Ay,{title:"Bor\xe7 #".concat((null==a?void 0:a.id.slice(-8))||"Detayı"),subtitle:"Bor\xe7 bilgilerini g\xf6r\xfcnt\xfcleyin",loading:N,error:f,backUrl:"/debts",actions:_,children:a&&(0,t.jsxs)(n.A7,{columns:2,children:[(0,t.jsx)(n.JH,{title:"Bor\xe7lu Bilgileri",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.Qn,{label:"Ad Soyad",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"font-medium",children:B()})]})}),(0,t.jsx)(n.Qn,{label:"Ad",value:a.name}),(0,t.jsx)(n.Qn,{label:"Soyad",value:a.surname}),(0,t.jsx)(n.Qn,{label:"Telefon",value:a.phone?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,t.jsx)("a",{href:"tel:".concat(a.phone),className:"text-purple-600 hover:text-purple-800 font-medium",children:(0,m.qH)(a.phone)})]}):(0,t.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})})]})}),(0,t.jsx)(n.JH,{title:"Bor\xe7 Bilgileri",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(n.Qn,{label:"Bor\xe7 Tutarı",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,t.jsxs)("span",{className:"font-bold text-red-600 text-lg",children:["₺",a.amount.toLocaleString("tr-TR")]})]})}),(0,t.jsx)(n.Qn,{label:"\xd6deme Durumu",value:a?a.is_paid?(0,t.jsx)(d.E,{variant:"success",children:"\xd6dendi"}):(0,t.jsx)(d.E,{variant:"danger",children:"\xd6denmedi"}):null}),(0,t.jsx)(n.Qn,{label:"Oluşturulma",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,t.jsx)("span",{children:(0,m.Yq)(a.created_at)})]})}),(0,t.jsx)(n.Qn,{label:"Son G\xfcncelleme",value:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,t.jsx)("span",{children:(0,m.Yq)(a.updated_at)})]})})]})}),(0,t.jsxs)(n.JH,{title:"Bor\xe7 Durumu",className:"md:col-span-2",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"text-center p-4 rounded-lg ".concat(a.is_paid?"bg-green-50":"bg-red-50"),children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Durum"}),(0,t.jsx)("div",{className:"text-lg font-semibold ".concat(a.is_paid?"text-green-600":"text-red-600"),children:a.is_paid?"\xd6dendi":"\xd6denmedi"})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Bor\xe7 Tutarı"}),(0,t.jsxs)("div",{className:"text-lg font-semibold text-blue-600",children:["₺",a.amount.toLocaleString("tr-TR")]})]}),(0,t.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Bor\xe7lu"}),(0,t.jsx)("div",{className:"text-lg font-semibold text-gray-700",children:B()})]})]}),!a.is_paid&&(0,t.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,t.jsx)("strong",{children:"\xd6deme Bekliyor:"})," Bu bor\xe7 hen\xfcz \xf6denmemiştir. \xd6deme yapıldığında otomatik olarak kasaya eklenecektir."]})]})}),a.is_paid&&(0,t.jsx)("div",{className:"mt-4 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-2 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-green-800",children:[(0,t.jsx)("strong",{children:"\xd6deme Tamamlandı:"})," Bu bor\xe7 başarıyla \xf6denmiştir. \xd6deme tutarı kasaya eklenmiştir."]})]})})]}),(0,t.jsx)(n.JH,{title:"Bor\xe7 \xd6zeti",className:"md:col-span-2",children:(0,t.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,t.jsxs)("div",{className:"flex items-center mb-2",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Bor\xe7 Fişi"})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-700 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Bor\xe7lu:"})," ",B()]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Telefon:"})," ",a.phone?(0,m.qH)(a.phone):"Belirtilmemiş"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Tutar:"})," ₺",a.amount.toLocaleString("tr-TR")]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Durum:"})," ",a.is_paid?"\xd6dendi":"\xd6denmedi"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Kayıt Tarihi:"})," ",(0,m.Yq)(a.created_at)]}),a.is_paid&&(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"\xd6deme Tarihi:"})," ",(0,m.Yq)(a.updated_at)]})]})]})})]})})}},5339:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7529:(e,a,s)=>{Promise.resolve().then(s.bind(s,2041))},7634:(e,a,s)=>{"use strict";s.d(a,{J:()=>l});var t=s(5731);let l={getAll:async()=>(await t.u.get("/debts")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await t.u.get("/debts/paginated?".concat(a))},async getById(e){try{return(await t.u.get("/debts/".concat(e))).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await t.u.post("/debts",e)},async update(e,a){await t.u.put("/debts/".concat(e),a)},async delete(e){await t.u.delete("/debts/".concat(e))},async pay(e,a){await t.u.post("/debts/".concat(e,"/pay"),a)},getUnpaid:async()=>(await t.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await t.u.get("/debts/paid")).data||[],async getByDateRange(e,a){let s=new URLSearchParams;return e&&s.append("start_date",e),a&&s.append("end_date",a),(await t.u.get("/debts/filter/date-range?".concat(s.toString()))).data||[]},getByPaymentStatus:async e=>(await t.u.get("/debts/filter/payment-status?is_paid=".concat(e))).data||[]}}},e=>{var a=a=>e(e.s=a);e.O(0,[122,809,41,441,684,977],()=>a(7529)),_N_E=e.O()}]);