(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[923],{321:(e,a,t)=>{Promise.resolve().then(t.bind(t,3873))},2657:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>f});var r=t(5155),s=t(2115),n=t(5695),l=t(5809),i=t(7703),d=t(3741),c=t(6440),o=t(3915),m=t(542),u=t(1007),p=t(1586),x=t(2657),h=t(3717),g=t(2525),y=t(4616),j=t(7924),b=t(7634);function f(){let e=(0,n.useRouter)(),[a,t]=(0,s.useState)([]),[f,v]=(0,s.useState)(!0),[N,w]=(0,s.useState)(!1),[A,C]=(0,s.useState)(!1),[k,_]=(0,s.useState)(!1),[S,T]=(0,s.useState)(null),[B,R]=(0,s.useState)(null),[E,z]=(0,s.useState)({name:"",surname:"",phone:"",amount:0}),[D,M]=(0,s.useState)(0),[P,F]=(0,s.useState)("unpaid"),[J,L]=(0,s.useState)(""),[O,Y]=(0,s.useState)({page:1,per_page:10}),[U,I]=(0,s.useState)(""),[H,W]=(0,s.useState)(""),[Z,G]=(0,s.useState)("");(0,s.useEffect)(()=>{K()},[P]);let K=async()=>{try{let e;if(v(!0),J||U)e=await b.J.getByDateRange(J,U),"all"!==P&&(e=e.filter(e=>"paid"===P?e.is_paid:!e.is_paid)),t(e);else switch(P){case"paid":e=await b.J.getPaid(),t(e);break;case"unpaid":e=await b.J.getUnpaid(),t(e);break;default:e=await b.J.getAll(),t(e)}}catch(e){console.error("Error loading debts:",e)}finally{v(!1)}},q=()=>{W(Z),Y(e=>({...e,page:1}))},Q=e=>{G(e)},V=async()=>{try{await b.J.create(E),w(!1),z({name:"",surname:"",phone:"",amount:0}),K()}catch(e){console.error("Error creating debt:",e)}},X=async()=>{if(S)try{let e={name:E.name,surname:E.surname,phone:E.phone,amount:E.amount};await b.J.update(S.id,e),C(!1),T(null),K()}catch(e){console.error("Error updating debt:",e)}},$=async e=>{if(confirm("Bu borcu silmek istediğinizden emin misiniz?"))try{await b.J.delete(e),K()}catch(e){console.error("Error deleting debt:",e)}},ee=async()=>{if(B)try{await b.J.pay(B.id,{amount:D}),_(!1),R(null),M(0),K()}catch(e){console.error("Error paying debt:",e)}},ea=e=>{T(e),z({name:e.name,surname:e.surname,phone:e.phone,amount:e.amount}),C(!0)},et=e=>{R(e),M(e.amount),_(!0)},er=e=>{F(e),Y({page:1,per_page:O.per_page})},es=e=>new Intl.NumberFormat("tr-TR",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e)+" TL",en=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),el=a.filter(e=>{if(!H)return!0;let a=en(H);return en(e.name).includes(a)||en(e.surname).includes(a)||en(e.phone).includes(a)}),ei=[{key:"customer",header:"M\xfcşteri",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:[e.name," ",e.surname]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.phone})]})]})},{key:"amount",header:"Tutar",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,r.jsx)("span",{className:"font-medium",children:es(e.amount)})]})},{key:"status",header:"Durum",render:e=>(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs ".concat(e.is_paid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.is_paid?"\xd6dendi":"\xd6denmedi"})},{key:"date",header:"Tarih",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("tr-TR")})},{key:"actions",header:"İşlemler",className:"text-right",render:a=>(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[!a.is_paid&&(0,r.jsx)(d.A,{size:"sm",variant:"warning",onClick:()=>et(a),title:"\xd6deme Yap",children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)(d.A,{size:"sm",variant:"success",onClick:()=>e.push("/debts/".concat(a.id)),title:"Detay",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})}),(0,r.jsx)(d.A,{size:"sm",variant:"info",onClick:()=>ea(a),title:"D\xfczenle",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(d.A,{size:"sm",variant:"danger",onClick:()=>$(a.id),title:"Sil",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})}];return f?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Bor\xe7lar"}),(0,r.jsx)("p",{className:"text-gray-600",children:"M\xfcşteri bor\xe7larını takip edin"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(d.A,{onClick:()=>w(!0),children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Yeni Bor\xe7"]})})]}),(0,r.jsxs)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[(0,r.jsx)("button",{onClick:()=>er("all"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("all"===P?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"T\xfcm\xfc"}),(0,r.jsx)("button",{onClick:()=>er("unpaid"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("unpaid"===P?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"\xd6denmemiş"}),(0,r.jsx)("button",{onClick:()=>er("paid"),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat("paid"===P?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:"\xd6denmiş"})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Tarih Aralığı Filtresi"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Başlangı\xe7 Tarihi"}),(0,r.jsx)("input",{type:"date",value:J,onChange:e=>L(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bitiş Tarihi"}),(0,r.jsx)("input",{type:"date",value:U,onChange:e=>I(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"flex space-x-2 pt-6",children:[(0,r.jsx)(d.A,{onClick:()=>{Y({page:1,per_page:O.per_page}),K()},variant:"primary",children:"Filtrele"}),(0,r.jsx)(d.A,{onClick:()=>{L(""),I(""),Y({page:1,per_page:O.per_page}),K()},variant:"secondary",children:"Temizle"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"M\xfcşteri adı, soyad veya telefon ara...",value:Z,onChange:e=>Q(e.target.value),onKeyDown:e=>"Enter"===e.key&&q(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsx)(d.A,{onClick:q,variant:"primary",className:"px-4 py-2",children:"Ara"}),(H||"unpaid"!==P||J||U)&&(0,r.jsx)(d.A,{onClick:()=>{W(""),G(""),F("unpaid"),L(""),I(""),Y(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),H&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[el.length,' bor\xe7 g\xf6steriliyor • "',H,'" araması']})]}),(0,r.jsx)(m.A,{columns:ei,data:el,pagination:{page:O.page,per_page:O.per_page,total:el.length,total_pages:Math.ceil(el.length/O.per_page),has_next:O.page<Math.ceil(el.length/O.per_page),has_prev:O.page>1},onPageChange:e=>{Y(a=>({...a,page:e}))},onPerPageChange:e=>{Y({page:1,per_page:e})},loading:f,emptyMessage:"Hen\xfcz bor\xe7 kaydı bulunmuyor",emptyIcon:(0,r.jsx)(p.A,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0}),(0,r.jsx)(c.A,{isOpen:N,onClose:()=>w(!1),title:"Yeni Bor\xe7 Ekle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(o.A,{label:"Ad",value:E.name,onChange:e=>z({...E,name:e.target.value}),placeholder:"M\xfcşteri adı"}),(0,r.jsx)(o.A,{label:"Soyad",value:E.surname,onChange:e=>z({...E,surname:e.target.value}),placeholder:"M\xfcşteri soyadı"}),(0,r.jsx)(o.A,{label:"Telefon",value:E.phone,onChange:e=>z({...E,phone:e.target.value}),placeholder:"0555 123 4567"}),(0,r.jsx)(o.A,{label:"Bor\xe7 Tutarı",type:"number",value:E.amount,onChange:e=>z({...E,amount:parseFloat(e.target.value)||0}),placeholder:"0.00"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(d.A,{variant:"secondary",onClick:()=>w(!1),children:"İptal"}),(0,r.jsx)(d.A,{onClick:V,children:"Bor\xe7 Ekle"})]})]})}),(0,r.jsx)(c.A,{isOpen:A,onClose:()=>C(!1),title:"Bor\xe7 D\xfczenle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(o.A,{label:"Ad",value:E.name,onChange:e=>z({...E,name:e.target.value}),placeholder:"M\xfcşteri adı"}),(0,r.jsx)(o.A,{label:"Soyad",value:E.surname,onChange:e=>z({...E,surname:e.target.value}),placeholder:"M\xfcşteri soyadı"}),(0,r.jsx)(o.A,{label:"Telefon",value:E.phone,onChange:e=>z({...E,phone:e.target.value}),placeholder:"0555 123 4567"}),(0,r.jsx)(o.A,{label:"Bor\xe7 Tutarı",type:"number",value:E.amount,onChange:e=>z({...E,amount:parseFloat(e.target.value)||0}),placeholder:"0.00"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(d.A,{variant:"secondary",onClick:()=>C(!1),children:"İptal"}),(0,r.jsx)(d.A,{onClick:X,children:"G\xfcncelle"})]})]})}),(0,r.jsx)(c.A,{isOpen:k,onClose:()=>_(!1),title:"Bor\xe7 \xd6de",children:(0,r.jsx)("div",{className:"space-y-4",children:B&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"font-medium text-gray-900",children:[B.name," ",B.surname]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Toplam Bor\xe7: ₺",B.amount]})]}),(0,r.jsx)(o.A,{label:"\xd6deme Tutarı",type:"number",value:D,onChange:e=>M(parseFloat(e.target.value)||0),placeholder:"0.00",helperText:"Maksimum: ₺".concat(B.amount)}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(d.A,{variant:"secondary",onClick:()=>_(!1),children:"İptal"}),(0,r.jsx)(d.A,{variant:"success",onClick:ee,children:"\xd6deme Yap"})]})]})})})]})})}},7634:(e,a,t)=>{"use strict";t.d(a,{J:()=>s});var r=t(5731);let s={getAll:async()=>(await r.u.get("/debts")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get("/debts/paginated?".concat(a))},async getById(e){try{return(await r.u.get("/debts/".concat(e))).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await r.u.post("/debts",e)},async update(e,a){await r.u.put("/debts/".concat(e),a)},async delete(e){await r.u.delete("/debts/".concat(e))},async pay(e,a){await r.u.post("/debts/".concat(e,"/pay"),a)},getUnpaid:async()=>(await r.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await r.u.get("/debts/paid")).data||[],async getByDateRange(e,a){let t=new URLSearchParams;return e&&t.append("start_date",e),a&&t.append("end_date",a),(await r.u.get("/debts/filter/date-range?".concat(t.toString()))).data||[]},getByPaymentStatus:async e=>(await r.u.get("/debts/filter/payment-status?is_paid=".concat(e))).data||[]}},7703:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>d,Zp:()=>l,aR:()=>i});var r=t(5155),s=t(2115),n=t(2596);let l=(0,s.forwardRef)((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.A)("bg-white rounded-lg border border-gray-200 shadow-sm",t),...l,children:s})});l.displayName="Card";let i=(0,s.forwardRef)((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.A)("px-6 py-4 border-b border-gray-200",t),...l,children:s})});i.displayName="CardHeader",(0,s.forwardRef)((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsx)("h3",{ref:a,className:(0,n.A)("text-lg font-semibold text-gray-900",t),...l,children:s})}).displayName="CardTitle",(0,s.forwardRef)((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsx)("p",{ref:a,className:(0,n.A)("text-sm text-gray-600 mt-1",t),...l,children:s})}).displayName="CardDescription";let d=(0,s.forwardRef)((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.A)("px-6 py-4",t),...l,children:s})});d.displayName="CardContent",(0,s.forwardRef)((e,a)=>{let{className:t,children:s,...l}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",t),...l,children:s})}).displayName="CardFooter"}},e=>{var a=a=>e(e.s=a);e.O(0,[122,269,809,564,441,684,977],()=>a(321)),_N_E=e.O()}]);