(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>o});var a=r(5155),s=r(2115),l=r(9959);let n=(0,s.createContext)(void 0);function o(e){let{children:t}=e,[r,o]=(0,s.useState)(null),[i,c]=(0,s.useState)(null),[u,d]=(0,s.useState)(null),[h,m]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=localStorage.getItem("auth_token"),t=localStorage.getItem("auth_user"),r=localStorage.getItem("auth_organization");if(e&&t)try{let a=JSON.parse(t);if(d(e),o(a),r){let e=JSON.parse(r);c(e)}}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}m(!1)},[]);let g=async e=>{try{let t=await l.y.login(e);d(t.token),o(t.user),c(t.organization||null),localStorage.setItem("auth_token",t.token),localStorage.setItem("auth_user",JSON.stringify(t.user)),t.organization&&localStorage.setItem("auth_organization",JSON.stringify(t.organization))}catch(e){throw console.error("Login error:",e),e}},f={user:r,organization:i,token:u,isLoading:h,isAuthenticated:!!r&&!!u,isAdmin:(null==r?void 0:r.role)==="admin",login:g,logout:()=>{d(null),o(null),c(null),localStorage.removeItem("auth_token"),localStorage.removeItem("auth_user"),localStorage.removeItem("auth_organization")}};return(0,a.jsx)(n.Provider,{value:f,children:t})}function i(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2596:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=function(){for(var e,t,r=0,a="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,a,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t)if(Array.isArray(t)){var l=t.length;for(r=0;r<l;r++)t[r]&&(a=e(t[r]))&&(s&&(s+=" "),s+=a)}else for(a in t)t[a]&&(s&&(s+=" "),s+=a);return s}(e))&&(a&&(a+=" "),a+=t);return a}},3741:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(5155),s=r(2115),l=r(2596);let n=(0,s.forwardRef)((e,t)=>{let{className:r,variant:s="primary",size:n="md",loading:o,children:i,disabled:c,...u}=e;return(0,a.jsxs)("button",{className:(0,l.A)("inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{"bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500":"primary"===s,"bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500":"secondary"===s,"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500":"danger"===s,"bg-green-600 text-white hover:bg-green-700 focus:ring-green-500":"success"===s,"bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500":"warning"===s,"bg-cyan-600 text-white hover:bg-cyan-700 focus:ring-cyan-500":"info"===s,"px-3 py-1.5 text-sm":"sm"===n,"px-4 py-2 text-sm":"md"===n,"px-6 py-3 text-base":"lg"===n},r),disabled:c||o,ref:t,...u,children:[o&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]})});n.displayName="Button";let o=n},3915:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(5155),s=r(2115),l=r(2596);let n=(0,s.forwardRef)((e,t)=>{let{className:r,label:s,error:n,helperText:o,type:i="text",...c}=e;return(0,a.jsxs)("div",{className:"w-full",children:[s&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,a.jsx)("input",{type:i,className:(0,l.A)("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",n&&"border-red-300 focus:ring-red-500 focus:border-red-500",r),ref:t,...c}),n&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:n}),o&&!n&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:o})]})});n.displayName="Input";let o=n},5339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},5731:(e,t,r)=>{"use strict";r.d(t,{u:()=>s});class a{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseURL).concat(e),a=localStorage.getItem("auth_token"),s={headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)},...t.headers},...t};try{let e=await fetch(r,s);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||"HTTP error! status: ".concat(e.status))}return await e.json()}catch(e){throw console.error("API request failed:",e),e}}async get(e,t){let r=e;if(t){let e=new URLSearchParams;Object.entries(t).forEach(t=>{let[r,a]=t;null!=a&&e.append(r,String(a))});let a=e.toString();a&&(r+="?".concat(a))}return this.request(r,{method:"GET"})}async post(e,t){return this.request(e,{method:"POST",body:JSON.stringify(t)})}async put(e,t){return this.request(e,{method:"PUT",body:JSON.stringify(t)})}async delete(e){return this.request(e,{method:"DELETE"})}constructor(e){this.baseURL=e}}let s=new a("http://localhost:5555/api/v1")},6303:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(5155),s=r(2115),l=r(5695),n=r(283),o=r(3741),i=r(3915),c=r(7703);let u=(0,r(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var d=r(5339),h=r(1007);function m(){let e=(0,l.useRouter)(),{login:t}=(0,n.A)(),[r,m]=(0,s.useState)({username:"",password:""}),[g,f]=(0,s.useState)(!1),[y,x]=(0,s.useState)(""),p=async a=>{a.preventDefault(),f(!0),x("");try{await t(r),e.push("/")}catch(e){console.error("Login error:",e),x("Kullanıcı adı veya şifre hatalı")}finally{f(!1)}},w=(e,t)=>{m(r=>({...r,[e]:t}))};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100",children:(0,a.jsx)(u,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Giriş Yap"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"İş Y\xf6netim Sistemine hoş geldiniz"})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Hesabınıza giriş yapın"})}),(0,a.jsx)(c.Wu,{children:(0,a.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[y&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-red-400"}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:y})})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(i.A,{label:"Kullanıcı Adı",type:"text",value:r.username,onChange:e=>w("username",e.target.value),placeholder:"Kullanıcı adınızı girin",required:!0,className:"relative"}),(0,a.jsx)(h.A,{className:"absolute left-3 top-9 h-5 w-5 text-gray-400"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(i.A,{label:"Şifre",type:"password",value:r.password,onChange:e=>w("password",e.target.value),placeholder:"Şifrenizi girin",required:!0,className:"relative"}),(0,a.jsx)(u,{className:"absolute left-3 top-9 h-5 w-5 text-gray-400"})]}),(0,a.jsx)("div",{children:(0,a.jsx)(o.A,{type:"submit",loading:g,className:"w-full",variant:"primary",children:g?"Giriş yapılıyor...":"Giriş Yap"})})]})})]})]})})}},7703:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>i,Zp:()=>n,aR:()=>o});var a=r(5155),s=r(2115),l=r(2596);let n=(0,s.forwardRef)((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.A)("bg-white rounded-lg border border-gray-200 shadow-sm",r),...n,children:s})});n.displayName="Card";let o=(0,s.forwardRef)((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.A)("px-6 py-4 border-b border-gray-200",r),...n,children:s})});o.displayName="CardHeader",(0,s.forwardRef)((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.A)("text-lg font-semibold text-gray-900",r),...n,children:s})}).displayName="CardTitle",(0,s.forwardRef)((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.A)("text-sm text-gray-600 mt-1",r),...n,children:s})}).displayName="CardDescription";let i=(0,s.forwardRef)((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.A)("px-6 py-4",r),...n,children:s})});i.displayName="CardContent",(0,s.forwardRef)((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",r),...n,children:s})}).displayName="CardFooter"},9176:(e,t,r)=>{Promise.resolve().then(r.bind(r,6303))},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},i=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:u="",children:d,iconNode:h,...m}=e;return(0,a.createElement)("svg",{ref:t,...c,width:s,height:s,stroke:r,strokeWidth:n?24*Number(l)/Number(s):l,className:o("lucide",u),...!d&&!i(m)&&{"aria-hidden":"true"},...m},[...h.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:i,...c}=r;return(0,a.createElement)(u,{ref:l,iconNode:t,className:o("lucide-".concat(s(n(e))),"lucide-".concat(e),i),...c})});return r.displayName=n(e),r}},9959:(e,t,r)=>{"use strict";r.d(t,{y:()=>s});var a=r(5731);let s={async login(e){let t=await a.u.post("/auth/login",e);if(!t.data)throw Error("Login failed: No data received");return t.data},async createUser(e){await a.u.post("/users",e)},getAllUsers:async()=>(await a.u.get("/users")).data||[],async getUsersPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await a.u.get("/users/paginated?".concat(t))},async getUserById(e){let t=await a.u.get("/users/".concat(e));if(!t.data)throw Error("User not found");return t.data},async updateUser(e,t){await a.u.put("/users/".concat(e),t)},async deleteUser(e){await a.u.delete("/users/".concat(e))}}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,977],()=>t(9176)),_N_E=e.O()}]);