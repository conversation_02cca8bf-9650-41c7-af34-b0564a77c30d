(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{984:(e,t,a)=>{Promise.resolve().then(a.bind(a,3792))},3109:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},3792:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var s=a(5155),r=a(2115),c=a(5695),n=a(283),i=a(5809),l=a(5876),d=a(7703),o=a(7108),u=a(1586),g=a(8048),m=a(3109),p=a(7109);function x(){let e=(0,c.useRouter)(),{isAdmin:t}=(0,n.A)(),[a,x]=(0,r.useState)(!0),[h,y]=(0,r.useState)({totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}),[f,j]=(0,r.useState)([]),[w,N]=(0,r.useState)([]);return((0,r.useEffect)(()=>{if(t)return void e.push("/admin")},[t,e]),(0,r.useEffect)(()=>{(async()=>{try{x(!0);let[e,t,a]=await Promise.all([p.y.getStats(),p.y.getRecentProducts(3),p.y.getRecentDebts(3)]);y(e),j(t),N(a)}catch(e){console.error("Error fetching dashboard data:",e)}finally{x(!1)}})()},[]),a)?(0,s.jsx)(l.A,{children:(0,s.jsx)(i.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):t?null:(0,s.jsx)(l.A,{children:(0,s.jsx)(i.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-gray-600",children:"İşletme y\xf6netim sisteminize hoş geldiniz"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam \xdcr\xfcn"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:h.totalProducts})]})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(u.A,{className:"h-8 w-8 text-red-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam Bor\xe7"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",h.totalDebts.toLocaleString()]})]})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(g.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Kasa Bakiyesi"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",h.totalSafeAmount.toLocaleString()]})]})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Net Bakiye"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",(h.totalSafeAmount-h.unpaidDebtsAmount).toLocaleString()]})]})]})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son \xdcr\xfcnler"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:f.length>0?f.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Kod: ",e.product_code]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["₺",e.price.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[e.quantity," adet"]})]})]},e.id)):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz \xfcr\xfcn bulunmuyor"})})})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son Bor\xe7lar"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:w.length>0?w.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[e.name," ",e.surname]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.phone})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-red-600",children:["₺",e.amount.toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.is_paid?"\xd6dendi":"\xd6denmemiş"})]})]},e.id)):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz bor\xe7 bulunmuyor"})})})})]})]})]})})})}},4957:(e,t,a)=>{"use strict";a.d(t,{U:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/categories")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/categories/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/categories/".concat(e))).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await s.u.post("/categories",e)},async update(e,t){await s.u.put("/categories/".concat(e),t)},async delete(e){await s.u.delete("/categories/".concat(e))}}},4975:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/campaigns")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/campaigns/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/campaigns/".concat(e))).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await s.u.post("/campaigns",e)},async update(e,t){await s.u.put("/campaigns/".concat(e),t)},async delete(e){await s.u.delete("/campaigns/".concat(e))},getActive:async()=>(await s.u.get("/campaigns/active")).data||[]}},5876:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(5155),r=a(2115),c=a(5695),n=a(283);function i(e){let{children:t,requireAdmin:a=!1}=e,{isAuthenticated:i,isAdmin:l,isLoading:d}=(0,n.A)(),o=(0,c.useRouter)();return((0,r.useEffect)(()=>{if(!d){if(!i)return void o.push("/login");if(a&&!l)return void o.push("/")}},[i,l,d,a,o]),d)?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):i&&(!a||l)?(0,s.jsx)(s.Fragment,{children:t}):null}},7109:(e,t,a)=>{"use strict";a.d(t,{y:()=>l});var s=a(9604),r=a(7634),c=a(9486),n=a(4957),i=a(4975);let l={async getStats(){try{let[e,t,a,l,d]=await Promise.all([s.j.getAll(),r.J.getAll(),c.x.getAll(),n.U.getAll(),i.A.getAll()]),o=a.reduce((e,t)=>e+t.amount,0),u=t.filter(e=>!e.is_paid).reduce((e,t)=>e+t.amount,0),g=new Date,m=d.filter(e=>new Date(e.end_date)>g).length;return{totalProducts:e.length,totalDebts:u,totalSafeAmount:o,unpaidDebtsAmount:u,totalCategories:l.length,activeCampaigns:m}}catch(e){return console.error("Error fetching dashboard stats:",e),{totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}}},async getRecentProducts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{return(await s.j.getAll()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent products:",e),[]}},async getRecentDebts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{return(await r.J.getUnpaid()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent debts:",e),[]}},async getTopSellingProducts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{return(await s.j.getAll()).sort((e,t)=>t.quantity-e.quantity).slice(0,e)}catch(e){return console.error("Error fetching top selling products:",e),[]}},async getRecentActivities(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{let[t,a]=await Promise.all([this.getRecentProducts(3),this.getRecentDebts(2)]),s=[];return t.forEach(e=>{s.push({id:e.id,type:"product",description:"Yeni \xfcr\xfcn eklendi: ".concat(e.name),amount:"₺".concat(e.price),time:this.formatTimeAgo(e.created_at)})}),a.forEach(e=>{s.push({id:e.id,type:"debt",description:"Yeni bor\xe7 kaydı: ".concat(e.name," ").concat(e.surname),amount:"₺".concat(e.amount),time:this.formatTimeAgo(e.created_at)})}),s.sort(()=>Math.random()-.5).slice(0,e)}catch(e){return console.error("Error fetching recent activities:",e),[]}},formatTimeAgo(e){let t=new Date(e),a=Math.floor((new Date().getTime()-t.getTime())/36e5);if(a<1)return"Az \xf6nce";{if(a<24)return"".concat(a," saat \xf6nce");let e=Math.floor(a/24);return"".concat(e," g\xfcn \xf6nce")}}}},7634:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/debts")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/debts/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/debts/".concat(e))).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await s.u.post("/debts",e)},async update(e,t){await s.u.put("/debts/".concat(e),t)},async delete(e){await s.u.delete("/debts/".concat(e))},async pay(e,t){await s.u.post("/debts/".concat(e,"/pay"),t)},getUnpaid:async()=>(await s.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await s.u.get("/debts/paid")).data||[],async getByDateRange(e,t){let a=new URLSearchParams;return e&&a.append("start_date",e),t&&a.append("end_date",t),(await s.u.get("/debts/filter/date-range?".concat(a.toString()))).data||[]},getByPaymentStatus:async e=>(await s.u.get("/debts/filter/payment-status?is_paid=".concat(e))).data||[]}},7703:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>l,Zp:()=>n,aR:()=>i});var s=a(5155),r=a(2115),c=a(2596);let n=(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,c.A)("bg-white rounded-lg border border-gray-200 shadow-sm",a),...n,children:r})});n.displayName="Card";let i=(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,c.A)("px-6 py-4 border-b border-gray-200",a),...n,children:r})});i.displayName="CardHeader",(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:(0,c.A)("text-lg font-semibold text-gray-900",a),...n,children:r})}).displayName="CardTitle",(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:(0,c.A)("text-sm text-gray-600 mt-1",a),...n,children:r})}).displayName="CardDescription";let l=(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,c.A)("px-6 py-4",a),...n,children:r})});l.displayName="CardContent",(0,r.forwardRef)((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,c.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",a),...n,children:r})}).displayName="CardFooter"},9486:(e,t,a)=>{"use strict";a.d(t,{x:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/safes")).data||[],async getById(e){let t=await s.u.get("/safes/".concat(e));if(!t.data)throw Error("Safe not found");return t.data},async create(e){await s.u.post("/safes",e)},async update(e,t){await s.u.put("/safes/".concat(e),t)},async delete(e){await s.u.delete("/safes/".concat(e))},async addMoney(e,t){await s.u.post("/safes/".concat(e,"/add-money"),t)},async withdrawMoney(e,t){await s.u.post("/safes/".concat(e,"/withdraw-money"),t)},getTotalAmount:async()=>(await s.u.get("/safes/total")).total_amount||0}},9604:(e,t,a)=>{"use strict";a.d(t,{j:()=>r});var s=a(5731);let r={getAll:async()=>(await s.u.get("/products")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get("/products/paginated?".concat(t))},async getById(e){try{return(await s.u.get("/products/".concat(e))).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let t={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.post("/products",t)},async update(e,t){let a={...t,campaign_id:t.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.put("/products/".concat(e),a)},async delete(e){await s.u.delete("/products/".concat(e))},search:async e=>(await s.u.get("/products/search?q=".concat(encodeURIComponent(e)))).data||[],importFromExcel:async e=>(await s.u.post("/products/import-excel",e)).data}}},e=>{var t=t=>e(e.s=t);e.O(0,[122,809,441,684,977],()=>t(984)),_N_E=e.O()}]);