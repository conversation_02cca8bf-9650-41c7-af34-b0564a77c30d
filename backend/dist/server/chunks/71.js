"use strict";exports.id=71,exports.ids=[71],exports.modules={252:(e,t,n)=>{n.d(t,{L:()=>o});var r=n(3210),l=n(8143);function o(){let[e]=(0,r.useState)(l.e);return e}},327:(e,t,n)=>{n.d(t,{Y:()=>o});var r=n(3210),l=n(2315);function o(e){let t=(0,r.useRef)(e);return(0,l.s)(()=>{t.current=e},[e]),t}},2263:(e,t,n)=>{n.d(t,{_:()=>o});var r=n(3210),l=n(327);let o=function(e){let t=(0,l.Y)(e);return r.useCallback((...e)=>t.current(...e),[t])}},2315:(e,t,n)=>{n.d(t,{s:()=>o});var r=n(3210),l=n(9923);let o=(e,t)=>{l._.isServer?(0,r.useEffect)(e,t):(0,r.useLayoutEffect)(e,t)}},3337:(e,t,n)=>{n.d(t,{x:()=>r});function r(...e){return Array.from(new Set(e.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}},3516:(e,t,n)=>{n.d(t,{e:()=>O,_:()=>A});var r,l,o=n(3210),i=n(252),a=n(2263),u=n(4818),s=n(2315),c=n(327),d=n(5319),f=n(4967),p=n(8143);"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&(null==(r=null==process?void 0:process.env)?void 0:r.NODE_ENV)==="test"&&void 0===(null==(l=null==Element?void 0:Element.prototype)?void 0:l.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join(`
`)),[]});var m=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(m||{}),h=n(9857),v=n(3337),g=n(4685),b=n(9334);function y(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:C)!==o.Fragment||1===o.Children.count(e.children)}let E=(0,o.createContext)(null);E.displayName="TransitionContext";var w=(e=>(e.Visible="visible",e.Hidden="hidden",e))(w||{});let F=(0,o.createContext)(null);function S(e){return"children"in e?S(e.children):e.current.filter(({el:e})=>null!==e.current).filter(({state:e})=>"visible"===e).length>0}function P(e,t){let n=(0,c.Y)(e),r=(0,o.useRef)([]),l=(0,u.a)(),s=(0,i.L)(),d=(0,a._)((e,t=b.mK.Hidden)=>{let o=r.current.findIndex(({el:t})=>t===e);-1!==o&&((0,g.Y)(t,{[b.mK.Unmount](){r.current.splice(o,1)},[b.mK.Hidden](){r.current[o].state="hidden"}}),s.microTask(()=>{var e;!S(r)&&l.current&&(null==(e=n.current)||e.call(n))}))}),f=(0,a._)(e=>{let t=r.current.find(({el:t})=>t===e);return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>d(e,b.mK.Unmount)}),p=(0,o.useRef)([]),m=(0,o.useRef)(Promise.resolve()),h=(0,o.useRef)({enter:[],leave:[]}),v=(0,a._)((e,n,r)=>{p.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(([t])=>t!==e)),null==t||t.chains.current[n].push([e,new Promise(e=>{p.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(h.current[n].map(([e,t])=>t)).then(()=>e())})]),"enter"===n?m.current=m.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),y=(0,a._)((e,t,n)=>{Promise.all(h.current[t].splice(0).map(([e,t])=>t)).then(()=>{var e;null==(e=p.current.shift())||e()}).then(()=>n(t))});return(0,o.useMemo)(()=>({children:r,register:f,unregister:d,onStart:v,onStop:y,wait:m,chains:h}),[f,d,r,v,y,h,m])}F.displayName="NestingContext";let C=o.Fragment,x=b.Ac.RenderStrategy,k=(0,b.FX)(function(e,t){let{show:n,appear:r=!1,unmount:l=!0,...i}=e,u=(0,o.useRef)(null),c=y(e),p=(0,f.P)(...c?[u,t]:null===t?[]:[t]);(0,d.g)();let m=(0,h.O_)();if(void 0===n&&null!==m&&(n=(m&h.Uw.Open)===h.Uw.Open),void 0===n)throw Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[v,g]=(0,o.useState)(n?"visible":"hidden"),w=P(()=>{n||g("hidden")}),[C,k]=(0,o.useState)(!0),A=(0,o.useRef)([n]);(0,s.s)(()=>{!1!==C&&A.current[A.current.length-1]!==n&&(A.current.push(n),k(!1))},[A,n]);let O=(0,o.useMemo)(()=>({show:n,appear:r,initial:C}),[n,r,C]);(0,s.s)(()=>{n?g("visible"):S(w)||null===u.current||g("hidden")},[n,w]);let R={unmount:l},N=(0,a._)(()=>{var t;C&&k(!1),null==(t=e.beforeEnter)||t.call(e)}),_=(0,a._)(()=>{var t;C&&k(!1),null==(t=e.beforeLeave)||t.call(e)}),D=(0,b.Ci)();return o.createElement(F.Provider,{value:w},o.createElement(E.Provider,{value:O},D({ourProps:{...R,as:o.Fragment,children:o.createElement(T,{ref:p,...R,...i,beforeEnter:N,beforeLeave:_})},theirProps:{},defaultTag:o.Fragment,features:x,visible:"visible"===v,name:"Transition"})))}),T=(0,b.FX)(function(e,t){var n,r;let{transition:l=!0,beforeEnter:u,afterEnter:c,beforeLeave:m,afterLeave:w,enter:k,enterFrom:T,enterTo:A,entered:O,leave:R,leaveFrom:N,leaveTo:_,...D}=e,[M,j]=(0,o.useState)(null),L=(0,o.useRef)(null),I=y(e),Y=(0,f.P)(...I?[L,t,j]:null===t?[]:[t]),U=null==(n=D.unmount)||n?b.mK.Unmount:b.mK.Hidden,{show:H,appear:$,initial:W}=function(){let e=(0,o.useContext)(E);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[X,V]=(0,o.useState)(H?"visible":"hidden"),B=function(){let e=(0,o.useContext)(F);if(null===e)throw Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:K,unregister:q}=B;(0,s.s)(()=>K(L),[K,L]),(0,s.s)(()=>{if(U===b.mK.Hidden&&L.current)return H&&"visible"!==X?void V("visible"):(0,g.Y)(X,{hidden:()=>q(L),visible:()=>K(L)})},[X,L,K,q,H,U]);let G=(0,d.g)();(0,s.s)(()=>{if(I&&G&&"visible"===X&&null===L.current)throw Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[L,X,G,I]);let z=$&&H&&W,Z=(0,o.useRef)(!1),J=P(()=>{Z.current||(V("hidden"),q(L))},B),[,Q]=function(e,t,n,r){let[l,a]=(0,o.useState)(n),{hasFlag:u,addFlag:c,removeFlag:d}=function(e=0){let[t,n]=(0,o.useState)(e),r=(0,o.useCallback)(e=>n(e),[t]),l=(0,o.useCallback)(e=>n(t=>t|e),[t]),i=(0,o.useCallback)(e=>(t&e)===e,[t]);return{flags:t,setFlag:r,addFlag:l,hasFlag:i,removeFlag:(0,o.useCallback)(e=>n(t=>t&~e),[n]),toggleFlag:(0,o.useCallback)(e=>n(t=>t^e),[n])}}(e&&l?3:0),f=(0,o.useRef)(!1),m=(0,o.useRef)(!1),h=(0,i.L)();return(0,s.s)(()=>{var l;if(e){if(n&&a(!0),!t){n&&c(3);return}return null==(l=null==r?void 0:r.start)||l.call(r,n),function(e,{prepare:t,run:n,done:r,inFlight:l}){let o=(0,p.e)();return function(e,{inFlight:t,prepare:n}){if(null!=t&&t.current)return n();let r=e.style.transition;e.style.transition="none",n(),e.offsetHeight,e.style.transition=r}(e,{prepare:t,inFlight:l}),o.nextFrame(()=>{n(),o.requestAnimationFrame(()=>{o.add(function(e,t){var n,r;let l=(0,p.e)();if(!e)return l.dispose;let o=!1;l.add(()=>{o=!0});let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===i.length?t():Promise.allSettled(i.map(e=>e.finished)).then(()=>{o||t()}),l.dispose}(e,r))})}),o.dispose}(t,{inFlight:f,prepare(){m.current?m.current=!1:m.current=f.current,f.current=!0,m.current||(n?(c(3),d(4)):(c(4),d(2)))},run(){m.current?n?(d(3),c(4)):(d(4),c(3)):n?d(1):c(1)},done(){var e;m.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(f.current=!1,d(7),n||a(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})}},[e,n,t,h]),e?[l,{closed:u(1),enter:u(2),leave:u(4),transition:u(2)||u(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}(!(!l||!I||!G||W&&!$),M,H,{start:(0,a._)(e=>{Z.current=!0,J.onStart(L,e?"enter":"leave",e=>{"enter"===e?null==u||u():"leave"===e&&(null==m||m())})}),end:(0,a._)(e=>{let t=e?"enter":"leave";Z.current=!1,J.onStop(L,t,e=>{"enter"===e?null==c||c():"leave"===e&&(null==w||w())}),"leave"!==t||S(J)||(V("hidden"),q(L))})}),ee=(0,b.oE)({ref:Y,className:(null==(r=(0,v.x)(D.className,z&&k,z&&T,Q.enter&&k,Q.enter&&Q.closed&&T,Q.enter&&!Q.closed&&A,Q.leave&&R,Q.leave&&!Q.closed&&N,Q.leave&&Q.closed&&_,!Q.transition&&H&&O))?void 0:r.trim())||void 0,...function(e){let t={};for(let n in e)!0===e[n]&&(t[`data-${n}`]="");return t}(Q)}),et=0;"visible"===X&&(et|=h.Uw.Open),"hidden"===X&&(et|=h.Uw.Closed),H&&"hidden"===X&&(et|=h.Uw.Opening),H||"visible"!==X||(et|=h.Uw.Closing);let en=(0,b.Ci)();return o.createElement(F.Provider,{value:J},o.createElement(h.El,{value:et},en({ourProps:ee,theirProps:D,defaultTag:C,features:x,visible:"visible"===X,name:"Transition.Child"})))}),A=(0,b.FX)(function(e,t){let n=null!==(0,o.useContext)(E),r=null!==(0,h.O_)();return o.createElement(o.Fragment,null,!n&&r?o.createElement(k,{ref:t,...e}):o.createElement(T,{ref:t,...e}))}),O=Object.assign(k,{Child:A,Root:k})},3567:(e,t,n)=>{var r=n(3210),l="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useSyncExternalStore,i=r.useRef,a=r.useEffect,u=r.useMemo,s=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var p=o(e,(d=u(function(){function e(e){if(!a){if(a=!0,o=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return i=t}return i=e}if(t=i,l(o,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(o=e,t):(o=e,i=n)}var o,i,a=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,c]))[0],d[1]);return a(function(){f.hasValue=!0,f.value=p},[p]),s(p),p}},4685:(e,t,n)=>{n.d(t,{Y:()=>r});function r(e,t,...n){if(e in t){let r=t[e];return"function"==typeof r?r(...n):r}let l=Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(e=>`"${e}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(l,r),l}},4818:(e,t,n)=>{n.d(t,{a:()=>o});var r=n(3210),l=n(2315);function o(){let e=(0,r.useRef)(!1);return(0,l.s)(()=>(e.current=!0,()=>{e.current=!1}),[]),e}},4967:(e,t,n)=>{n.d(t,{P:()=>a,a:()=>i});var r=n(3210),l=n(2263);let o=Symbol();function i(e,t=!0){return Object.assign(e,{[o]:t})}function a(...e){let t=(0,r.useRef)(e),n=(0,l._)(e=>{for(let n of t.current)null!=n&&("function"==typeof n?n(e):n.current=e)});return e.every(e=>null==e||(null==e?void 0:e[o]))?void 0:n}},5319:(e,t,n)=>{n.d(t,{g:()=>i});var r,l=n(3210),o=n(9923);function i(){let e,t=(e="undefined"==typeof document,"useSyncExternalStore"in(r||(r=n.t(l,2)))&&(0,(r||(r=n.t(l,2))).useSyncExternalStore)(()=>()=>{},()=>!1,()=>!e)),[i,a]=l.useState(o._.isHandoffComplete);return i&&!1===o._.isHandoffComplete&&a(!1),l.useEffect(()=>{!0!==i&&a(!0)},[i]),l.useEffect(()=>o._.handoff(),[]),!t&&i}},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6895:(e,t,n)=>{e.exports=n(3567)},8143:(e,t,n)=>{n.d(t,{e:()=>function e(){let t=[],n={addEventListener:(e,t,r,l)=>(e.addEventListener(t,r,l),n.add(()=>e.removeEventListener(t,r,l))),requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return n.add(()=>cancelAnimationFrame(t))},nextFrame:(...e)=>n.requestAnimationFrame(()=>n.requestAnimationFrame(...e)),setTimeout(...e){let t=setTimeout(...e);return n.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return(0,r._)(()=>{t.current&&e[0]()}),n.add(()=>{t.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(t){let n=e();return t(n),this.add(()=>n.dispose())},add:e=>(t.includes(e)||t.push(e),()=>{let n=t.indexOf(e);if(n>=0)for(let e of t.splice(n,1))e()}),dispose(){for(let e of t.splice(0))e()}};return n}});var r=n(9704)},8233:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8908:(e,t,n)=>{n.d(t,{lG:()=>ez});var r,l,o,i=n(3210),a=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(a||{}),u=n(327);class s extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}var c=n(8143),d=Object.defineProperty,f=(e,t,n)=>t in e?d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t,n)=>(f(e,"symbol"!=typeof t?t+"":t,n),n),m=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},h=(e,t,n)=>(m(e,t,"read from private field"),n?n.call(e):t.get(e)),v=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},g=(e,t,n,r)=>(m(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class b{constructor(e){v(this,r,{}),v(this,l,new s(()=>new Set)),v(this,o,new Set),p(this,"disposables",(0,c.e)()),g(this,r,e)}dispose(){this.disposables.dispose()}get state(){return h(this,r)}subscribe(e,t){let n={selector:e,callback:t,current:e(h(this,r))};return h(this,o).add(n),this.disposables.add(()=>{h(this,o).delete(n)})}on(e,t){return h(this,l).get(e).add(t),this.disposables.add(()=>{h(this,l).get(e).delete(t)})}send(e){let t=this.reduce(h(this,r),e);if(t!==h(this,r)){for(let e of(g(this,r,t),h(this,o))){let t=e.selector(h(this,r));y(e.current,t)||(e.current=t,e.callback(t))}for(let t of h(this,l).get(e.type))t(h(this,r),e)}}}function y(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&E(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&E(e.entries(),t.entries()):!!(w(e)&&w(t))&&E(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function E(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function w(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}r=new WeakMap,l=new WeakMap,o=new WeakMap;var F=n(4685),S=Object.defineProperty,P=(e,t,n)=>t in e?S(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,C=(e,t,n)=>(P(e,"symbol"!=typeof t?t+"":t,n),n),x=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(x||{});let k={0(e,t){let n=t.id,r=e.stack,l=e.stack.indexOf(n);if(-1!==l){let t=e.stack.slice();return t.splice(l,1),t.push(n),r=t,{...e,stack:r}}return{...e,stack:[...e.stack,n]}},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let l=e.stack.slice();return l.splice(r,1),{...e,stack:l}}};class T extends b{constructor(){super(...arguments),C(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),C(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new T({stack:[]})}reduce(e,t){return(0,F.Y)(t.type,k,e,t)}}let A=new s(()=>T.new());var O=n(6895),R=n(2263);function N(e,t,n=y){return(0,O.useSyncExternalStoreWithSelector)((0,R._)(t=>e.subscribe(_,t)),(0,R._)(()=>e.state),(0,R._)(()=>e.state),(0,R._)(t),n)}function _(e){return e}var D=n(2315);function M(e,t){let n=(0,i.useId)(),r=A.get(t),[l,o]=N(r,(0,i.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return(0,D.s)(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!o||l)}var j=n(9923);function L(e){var t,n;return j._.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let I=new Map,Y=new Map;function U(e){var t;let n=null!=(t=Y.get(e))?t:0;return Y.set(e,n+1),0!==n||(I.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>(function(e){var t;let n=null!=(t=Y.get(e))?t:1;if(1===n?Y.delete(e):Y.set(e,n-1),1!==n)return;let r=I.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,I.delete(e))})(e)}function H(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function $(e){return H(e)&&"tagName"in e}function W(e){return $(e)&&"accessKey"in e}function X(e){return $(e)&&"tabIndex"in e}let V=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(","),B=["[data-autofocus]"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var K=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(K||{}),q=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(q||{}),G=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(G||{}),z=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(z||{}),Z=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Z||{});function J(e){null==e||e.focus({preventScroll:!0})}function Q(e,t,{sorted:n=!0,relativeTo:r=null,skipElements:l=[]}={}){var o,i,a;let u=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,s=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort((e,n)=>{let r=t(e),l=t(n);if(null===r||null===l)return 0;let o=r.compareDocumentPosition(l);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(B)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(V)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e);l.length>0&&s.length>1&&(s=s.filter(e=>!l.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),r=null!=r?r:u.activeElement;let c=(()=>{if(5&t)return 1;if(10&t)return -1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,s.indexOf(r))-1;if(4&t)return Math.max(0,s.indexOf(r))+1;if(8&t)return s.length-1;throw Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),f=32&t?{preventScroll:!0}:{},p=0,m=s.length,h;do{if(p>=m||p+m<=0)return 0;let e=d+p;if(16&t)e=(e+m)%m;else{if(e<0)return 3;if(e>=m)return 1}null==(h=s[e])||h.focus(f),p+=c}while(h!==u.activeElement);return 6&t&&null!=(a=null==(i=null==(o=h)?void 0:o.matches)?void 0:i.call(o,"textarea,input"))&&a&&h.select(),2}function ee(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function et(){return ee()||/Android/gi.test(window.navigator.userAgent)}function en(...e){return(0,i.useMemo)(()=>L(...e),[...e])}var er=n(9334),el=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(el||{});let eo=(0,er.FX)(function(e,t){var n;let{features:r=1,...l}=e,o={ref:t,"aria-hidden":(2&r)==2||(null!=(n=l["aria-hidden"])?n:void 0),hidden:(4&r)==4||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(4&r)==4&&(2&r)!=2&&{display:"none"}}};return(0,er.Ci)()({ourProps:o,theirProps:l,slot:{},defaultTag:"span",name:"Hidden"})}),ei=(0,i.createContext)(null);function ea({children:e,node:t}){let[n,r]=(0,i.useState)(null),l=eu(null!=t?t:n);return i.createElement(ei.Provider,{value:l},e,null===l&&i.createElement(eo,{features:el.Hidden,ref:e=>{var t,n;if(e){for(let l of null!=(n=null==(t=L(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(l!==document.body&&l!==document.head&&$(l)&&null!=l&&l.contains(e)){r(l);break}}}}))}function eu(e=null){var t;return null!=(t=(0,i.useContext)(ei))?t:e}let es=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e,...l){let o=t[e].call(n,...l);o&&(n=o,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:(0,c.e)(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let r,l={doc:e,d:t,meta:function(e){let t={};for(let n of e)Object.assign(t,n(t));return t}(n)},o=[ee()?{before({doc:e,d:t,meta:n}){function r(e){return n.containers.flatMap(e=>e()).some(t=>t.contains(e))}t.microTask(()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=(0,c.e)();n.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>n.dispose()))}let l=null!=(n=window.scrollY)?n:window.pageYOffset,o=null;t.addEventListener(e,"click",t=>{if(X(t.target))try{let n=t.target.closest("a");if(!n)return;let{hash:l}=new URL(n.href),i=e.querySelector(l);X(i)&&!r(i)&&(o=i)}catch{}},!0),t.addEventListener(e,"touchstart",e=>{var n;if(X(e.target)&&$(n=e.target)&&"style"in n)if(r(e.target)){let n=e.target;for(;n.parentElement&&r(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")}),t.addEventListener(e,"touchmove",e=>{if(X(e.target)){var t;if(!(W(t=e.target)&&"INPUT"===t.nodeName))if(r(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),t.add(()=>{var e;l!==(null!=(e=window.scrollY)?e:window.pageYOffset)&&window.scrollTo(0,l),o&&o.isConnected&&(o.scrollIntoView({block:"nearest"}),o=null)})})}}:{},{before({doc:e}){var t;let n=e.documentElement;r=Math.max(0,(null!=(t=e.defaultView)?t:window).innerWidth-n.clientWidth)},after({doc:e,d:t}){let n=e.documentElement,l=Math.max(0,n.clientWidth-n.offsetWidth),o=Math.max(0,r-l);t.style(n,"paddingRight",`${o}px`)}},{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];o.forEach(({before:e})=>null==e?void 0:e(l)),o.forEach(({after:e})=>null==e?void 0:e(l))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});es.subscribe(()=>{let e=es.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&es.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&es.dispatch("TEARDOWN",n)}});var ec=n(5319),ed=n(4967);let ef=(0,i.createContext)(()=>{});function ep({value:e,children:t}){return i.createElement(ef.Provider,{value:e},t)}var em=n(9857);let eh=(0,i.createContext)(!1);function ev(e){return i.createElement(eh.Provider,{value:e.force},e.children)}let eg=(0,i.createContext)(void 0),eb=(0,i.createContext)(null);eb.displayName="DescriptionContext";let ey=Object.assign((0,er.FX)(function(e,t){let n=(0,i.useId)(),r=(0,i.useContext)(eg),{id:l=`headlessui-description-${n}`,...o}=e,a=function e(){let t=(0,i.useContext)(eb);if(null===t){let t=Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(t,e),t}return t}(),u=(0,ed.P)(t);(0,D.s)(()=>a.register(l),[l,a.register]);let s=r||!1,c=(0,i.useMemo)(()=>({...a.slot,disabled:s}),[a.slot,s]),d={ref:u,...a.props,id:l};return(0,er.Ci)()({ourProps:d,theirProps:o,slot:c,defaultTag:"p",name:a.name||"Description"})}),{});var eE=n(252),ew=n(4818),eF=n(9704);function eS(e){(0,R._)(e),(0,i.useRef)(!1)}var eP=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(eP||{});function eC(e,t){(0,i.useRef)([]),(0,R._)(e)}let ex=[];function ek(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)$(n.current)&&t.add(n.current);return t}var eT=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(eT||{});let eA=Object.assign((0,er.FX)(function(e,t){var n;let r,l=(0,i.useRef)(null),o=(0,ed.P)(l,t),{initialFocus:a,initialFocusFallback:s,containers:c,features:d=15,...f}=e;(0,ec.g)()||(d=0);let p=en(l);!function(e,{ownerDocument:t}){let n=!!(8&e),r=function(e=!0){let t=(0,i.useRef)(ex.slice());return eC(([e],[n])=>{!0===n&&!1===e&&(0,eF._)(()=>{t.current.splice(0)}),!1===n&&!0===e&&(t.current=ex.slice())},[e,ex,t]),(0,R._)(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(n);eC(()=>{n||(null==t?void 0:t.activeElement)===(null==t?void 0:t.body)&&J(r())},[n]),eS(()=>{n&&J(r())})}(d,{ownerDocument:p});let m=function(e,{ownerDocument:t,container:n,initialFocus:r,initialFocusFallback:l}){let o=(0,i.useRef)(null),a=M(!!(1&e),"focus-trap#initial-focus"),u=(0,ew.a)();return eC(()=>{if(0===e)return;if(!a){null!=l&&l.current&&J(l.current);return}let i=n.current;i&&(0,eF._)(()=>{if(!u.current)return;let n=null==t?void 0:t.activeElement;if(null!=r&&r.current){if((null==r?void 0:r.current)===n){o.current=n;return}}else if(i.contains(n)){o.current=n;return}if(null!=r&&r.current)J(r.current);else{if(16&e){if(Q(i,K.First|K.AutoFocus)!==q.Error)return}else if(Q(i,K.First)!==q.Error)return;if(null!=l&&l.current&&(J(l.current),(null==t?void 0:t.activeElement)===l.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}o.current=null==t?void 0:t.activeElement})},[l,a,e]),o}(d,{ownerDocument:p,container:l,initialFocus:a,initialFocusFallback:s});!function(e,{ownerDocument:t,container:n,containers:r,previousActiveElement:l}){var o;let i=(0,ew.a)(),a=!!(4&e);null==t||t.defaultView,(0,u.Y)(e=>{if(!a||!i.current)return;let t=ek(r);W(n.current)&&t.add(n.current);let o=l.current;if(!o)return;let u=e.target;W(u)?eO(t,u)?(l.current=u,J(u)):(e.preventDefault(),e.stopPropagation(),J(o)):J(l.current)})}(d,{ownerDocument:p,container:l,containers:c,previousActiveElement:m});let h=(r=(0,i.useRef)(0),n=e=>{"Tab"===e.key&&(r.current=+!!e.shiftKey)},(0,u.Y)(n),r),v=(0,R._)(e=>{if(!W(l.current))return;let t=l.current;(0,F.Y)(h.current,{[eP.Forwards]:()=>{Q(t,K.First,{skipElements:[e.relatedTarget,s]})},[eP.Backwards]:()=>{Q(t,K.Last,{skipElements:[e.relatedTarget,s]})}})}),g=M(!!(2&d),"focus-trap#tab-lock"),b=(0,eE.L)(),y=(0,i.useRef)(!1),E=(0,er.Ci)();return i.createElement(i.Fragment,null,g&&i.createElement(eo,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:el.Focusable}),E({ourProps:{ref:o,onKeyDown(e){"Tab"==e.key&&(y.current=!0,b.requestAnimationFrame(()=>{y.current=!1}))},onBlur(e){if(!(4&d))return;let t=ek(c);W(l.current)&&t.add(l.current);let n=e.relatedTarget;X(n)&&"true"!==n.dataset.headlessuiFocusGuard&&(eO(t,n)||(y.current?Q(l.current,(0,F.Y)(h.current,{[eP.Forwards]:()=>K.Next,[eP.Backwards]:()=>K.Previous})|K.WrapAround,{relativeTo:e.target}):X(e.target)&&J(e.target)))}},theirProps:f,defaultTag:"div",name:"FocusTrap"}),g&&i.createElement(eo,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:v,features:el.Focusable}))}),{features:eT});function eO(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var eR=n(1215);let eN=i.Fragment,e_=(0,er.FX)(function(e,t){let{ownerDocument:n=null,...r}=e,l=(0,i.useRef)(null),o=(0,ed.P)((0,ed.a)(e=>{l.current=e}),t),a=en(l),u=null!=n?n:a,s=function(e){let t=(0,i.useContext)(eh),n=(0,i.useContext)(eM),[r,l]=(0,i.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(j._.isServer)return null;let l=null==e?void 0:e.getElementById("headlessui-portal-root");if(l)return l;if(null===e)return null;let o=e.createElement("div");return o.setAttribute("id","headlessui-portal-root"),e.body.appendChild(o)});return r}(u),[c]=(0,i.useState)(()=>{var e;return j._.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null}),d=(0,i.useContext)(ej),f=(0,ec.g)();(0,D.s)(()=>{!s||!c||s.contains(c)||(c.setAttribute("data-headlessui-portal",""),s.appendChild(c))},[s,c]),(0,D.s)(()=>{if(c&&d)return d.register(c)},[d,c]),eS(()=>{var e;s&&c&&(H(c)&&s.contains(c)&&s.removeChild(c),s.childNodes.length<=0&&(null==(e=s.parentElement)||e.removeChild(s)))});let p=(0,er.Ci)();return f&&s&&c?(0,eR.createPortal)(p({ourProps:{ref:o},theirProps:r,slot:{},defaultTag:eN,name:"Portal"}),c):null}),eD=i.Fragment,eM=(0,i.createContext)(null),ej=(0,i.createContext)(null),eL=(0,er.FX)(function(e,t){let n=(0,ed.P)(t),{enabled:r=!0,ownerDocument:l,...o}=e,a=(0,er.Ci)();return r?i.createElement(e_,{...o,ownerDocument:l,ref:n}):a({ourProps:{ref:n},theirProps:o,slot:{},defaultTag:eN,name:"Portal"})}),eI=(0,er.FX)(function(e,t){let{target:n,...r}=e,l={ref:(0,ed.P)(t)},o=(0,er.Ci)();return i.createElement(eM.Provider,{value:n},o({ourProps:l,theirProps:r,defaultTag:eD,name:"Popover.Group"}))}),eY=Object.assign(eL,{Group:eI});var eU=n(3516),eH=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(eH||{}),e$=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(e$||{});let eW={0:(e,t)=>e.titleId===t.id?e:{...e,titleId:t.id}},eX=(0,i.createContext)(null);function eV(e){let t=(0,i.useContext)(eX);if(null===t){let t=Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,eV),t}return t}function eB(e,t){return(0,F.Y)(t.type,eW,e,t)}eX.displayName="DialogContext";let eK=(0,er.FX)(function(e,t){let n,r,l,o,s,d,f,p,m,h=(0,i.useId)(),{id:v=`headlessui-dialog-${h}`,open:g,onClose:b,initialFocus:y,role:E="dialog",autoFocus:w=!0,__demoMode:S=!1,unmount:P=!1,...C}=e,x=(0,i.useRef)(!1);E="dialog"===E||"alertdialog"===E?E:(x.current||(x.current=!0,console.warn(`Invalid role [${E}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog");let k=(0,em.O_)();void 0===g&&null!==k&&(g=(k&em.Uw.Open)===em.Uw.Open);let T=(0,i.useRef)(null),O=(0,ed.P)(T,t),_=en(T),j=+!g,[I,Y]=(0,i.useReducer)(eB,{titleId:null,descriptionId:null,panelRef:(0,i.createRef)()}),H=(0,R._)(()=>b(!1)),B=(0,R._)(e=>Y({type:0,id:e})),K=!!(0,ec.g)()&&0===j,[q,G]=(n=(0,i.useContext)(ej),r=(0,i.useRef)([]),l=(0,R._)(e=>(r.current.push(e),n&&n.register(e),()=>o(e))),o=(0,R._)(e=>{let t=r.current.indexOf(e);-1!==t&&r.current.splice(t,1),n&&n.unregister(e)}),s=(0,i.useMemo)(()=>({register:l,unregister:o,portals:r}),[l,o,r]),[r,(0,i.useMemo)(()=>function({children:e}){return i.createElement(ej.Provider,{value:s},e)},[s])]),Z=eu(),{resolveContainers:J}=function({defaultContainers:e=[],portals:t,mainTreeNode:n}={}){let r=en(n),l=(0,R._)(()=>{var l,o;let i=[];for(let t of e)null!==t&&($(t)?i.push(t):"current"in t&&$(t.current)&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(l=null==r?void 0:r.querySelectorAll("html > *, body > *"))?l:[])e!==document.body&&e!==document.head&&$(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(o=null==n?void 0:n.getRootNode())?void 0:o.host))||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:l,contains:(0,R._)(e=>l().some(t=>t.contains(e)))}}({mainTreeNode:Z,portals:q,defaultContainers:[{get current(){var Q;return null!=(Q=I.panelRef.current)?Q:T.current}}]}),ee=null!==k&&(k&em.Uw.Closing)===em.Uw.Closing;!function(e,{allowed:t,disallowed:n}={}){let r=M(e,"inert-others");(0,D.s)(()=>{var e,l;if(!r)return;let o=(0,c.e)();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&o.add(U(t));let i=null!=(l=null==t?void 0:t())?l:[];for(let e of i){if(!e)continue;let t=L(e);if(!t)continue;let n=e.parentElement;for(;n&&n!==t.body;){for(let e of n.children)i.some(t=>e.contains(t))||o.add(U(e));n=n.parentElement}}return o.dispose},[r,t,n])}(!S&&!ee&&K,{allowed:(0,R._)(()=>{var e,t;return[null!=(t=null==(e=T.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:(0,R._)(()=>{var e;return[null!=(e=null==Z?void 0:Z.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let el=A.get(null);(0,D.s)(()=>{if(K)return el.actions.push(v),()=>el.actions.pop(v)},[el,v,K]);let eo=N(el,(0,i.useCallback)(e=>el.selectors.isTop(e,v),[el,v]));d=(0,u.Y)(e=>{e.preventDefault(),H()}),f=(0,i.useCallback)(function(e,t){if(e.defaultPrevented)return;let n=t(e);if(null!==n&&n.getRootNode().contains(n)&&n.isConnected){for(let t of function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(J))if(null!==t&&(t.contains(n)||e.composed&&e.composedPath().includes(t)))return;return function(e,t=0){var n;return e!==(null==(n=L(e))?void 0:n.body)&&(0,F.Y)(t,{0:()=>e.matches(V),1(){let t=e;for(;null!==t;){if(t.matches(V))return!0;t=t.parentElement}return!1}})}(n,z.Loose)||-1===n.tabIndex||e.preventDefault(),d.current(e,n)}},[d,J]),p=(0,i.useRef)(null),(0,u.Y)(e=>{var t,n;et()||(p.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),(0,u.Y)(e=>{if(et()||!p.current)return;let t=p.current;return p.current=null,f(e,()=>t)}),m=(0,i.useRef)({x:0,y:0}),(0,u.Y)(e=>{m.current.x=e.touches[0].clientX,m.current.y=e.touches[0].clientY}),(0,u.Y)(e=>{let t={x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY};if(!(Math.abs(t.x-m.current.x)>=30||Math.abs(t.y-m.current.y)>=30))return f(e,()=>X(e.target)?e.target:null)}),(0,u.Y)(e=>f(e,()=>{var e;return W(e=window.document.activeElement)&&"IFRAME"===e.nodeName?window.document.activeElement:null})),function(e,t="undefined"!=typeof document?document.defaultView:null,n){let r=M(e,"escape");(0,u.Y)(e=>{r&&(e.defaultPrevented||e.key===a.Escape&&n(e))})}(eo,null==_?void 0:_.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),H()}),function(e,t,n=()=>[document.body]){!function(e,t,n=()=>({containers:[]})){let r=(0,i.useSyncExternalStore)(es.subscribe,es.getSnapshot,es.getSnapshot),l=t?r.get(t):void 0;l&&l.count,(0,D.s)(()=>{if(!(!t||!e))return es.dispatch("PUSH",t,n),()=>es.dispatch("POP",t,n)},[e,t])}(M(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}(!S&&!ee&&K,_,J),(0,u.Y)(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&H()});let[ei,ea]=function(){let[e,t]=(0,i.useState)([]);return[e.length>0?e.join(" "):void 0,(0,i.useMemo)(()=>function(e){let n=(0,R._)(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return -1!==r&&n.splice(r,1),n}))),r=(0,i.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return i.createElement(eb.Provider,{value:r},e.children)},[t])]}(),ef=(0,i.useMemo)(()=>[{dialogState:j,close:H,setTitleId:B,unmount:P},I],[j,I,H,B,P]),eh=(0,i.useMemo)(()=>({open:0===j}),[j]),eg={ref:O,id:v,role:E,tabIndex:-1,"aria-modal":S?void 0:0===j||void 0,"aria-labelledby":I.titleId,"aria-describedby":ei,unmount:P},ey=!function(){var e;let[t]=(0,i.useState)(()=>null),[n,r]=(0,i.useState)(null!=(e=null==t?void 0:t.matches)&&e);return(0,D.s)(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),eE=eT.None;K&&!S&&(eE|=eT.RestoreFocus,eE|=eT.TabLock,w&&(eE|=eT.AutoFocus),ey&&(eE|=eT.InitialFocus));let ew=(0,er.Ci)();return i.createElement(em.$x,null,i.createElement(ev,{force:!0},i.createElement(eY,null,i.createElement(eX.Provider,{value:ef},i.createElement(eI,{target:T},i.createElement(ev,{force:!1},i.createElement(ea,{slot:eh},i.createElement(G,null,i.createElement(eA,{initialFocus:y,initialFocusFallback:T,containers:J,features:eE},i.createElement(ep,{value:H},ew({ourProps:eg,theirProps:C,slot:eh,defaultTag:eq,features:eG,visible:0===j,name:"Dialog"})))))))))))}),eq="div",eG=er.Ac.RenderStrategy|er.Ac.Static,ez=Object.assign((0,er.FX)(function(e,t){let{transition:n=!1,open:r,...l}=e,o=(0,em.O_)(),a=e.hasOwnProperty("open")||null!==o,u=e.hasOwnProperty("onClose");if(!a&&!u)throw Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!a)throw Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!o&&"boolean"!=typeof e.open)throw Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if("function"!=typeof e.onClose)throw Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(void 0!==r||n)&&!l.static?i.createElement(ea,null,i.createElement(eU.e,{show:r,transition:n,unmount:l.unmount},i.createElement(eK,{ref:t,...l}))):i.createElement(ea,null,i.createElement(eK,{ref:t,open:r,...l}))}),{Panel:(0,er.FX)(function(e,t){let n=(0,i.useId)(),{id:r=`headlessui-dialog-panel-${n}`,transition:l=!1,...o}=e,[{dialogState:a,unmount:u},s]=eV("Dialog.Panel"),c=(0,ed.P)(t,s.panelRef),d=(0,i.useMemo)(()=>({open:0===a}),[a]),f=(0,R._)(e=>{e.stopPropagation()}),p=l?eU._:i.Fragment,m=(0,er.Ci)();return i.createElement(p,{...l?{unmount:u}:{}},m({ourProps:{ref:c,id:r,onClick:f},theirProps:o,slot:d,defaultTag:"div",name:"Dialog.Panel"}))}),Title:((0,er.FX)(function(e,t){let{transition:n=!1,...r}=e,[{dialogState:l,unmount:o}]=eV("Dialog.Backdrop"),a=(0,i.useMemo)(()=>({open:0===l}),[l]),u=n?eU._:i.Fragment,s=(0,er.Ci)();return i.createElement(u,{...n?{unmount:o}:{}},s({ourProps:{ref:t,"aria-hidden":!0},theirProps:r,slot:a,defaultTag:"div",name:"Dialog.Backdrop"}))}),(0,er.FX)(function(e,t){let n=(0,i.useId)(),{id:r=`headlessui-dialog-title-${n}`,...l}=e,[{dialogState:o,setTitleId:a}]=eV("Dialog.Title"),u=(0,ed.P)(t),s=(0,i.useMemo)(()=>({open:0===o}),[o]);return(0,er.Ci)()({ourProps:{ref:u,id:r},theirProps:l,slot:s,defaultTag:"h2",name:"Dialog.Title"})})),Description:ey})},9334:(e,t,n)=>{n.d(t,{Ac:()=>i,Ci:()=>u,FX:()=>f,mK:()=>a,oE:()=>p});var r=n(3210),l=n(3337),o=n(4685),i=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(i||{}),a=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(a||{});function u(){let e,t,n=(e=(0,r.useRef)([]),t=(0,r.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]),(...n)=>{if(!n.every(e=>null==e))return e.current=n,t});return(0,r.useCallback)(e=>(function({ourProps:e,theirProps:t,slot:n,defaultTag:r,features:l,visible:i=!0,name:a,mergeRefs:u}){u=null!=u?u:c;let f=d(t,e);if(i)return s(f,n,r,a,u);let p=null!=l?l:0;if(2&p){let{static:e=!1,...t}=f;if(e)return s(t,n,r,a,u)}if(1&p){let{unmount:e=!0,...t}=f;return(0,o.Y)(+!e,{0:()=>null,1:()=>s({...t,hidden:!0,style:{display:"none"}},n,r,a,u)})}return s(f,n,r,a,u)})({mergeRefs:n,...e}),[n])}function s(e,t={},n,o,i){let{as:a=n,children:u,refName:c="ref",...f}=m(e,["unmount","static"]),h=void 0!==e.ref?{[c]:e.ref}:{},v="function"==typeof u?u(t):u;"className"in f&&f.className&&"function"==typeof f.className&&(f.className=f.className(t)),f["aria-labelledby"]&&f["aria-labelledby"]===f.id&&(f["aria-labelledby"]=void 0);let g={};if(t){let e=!1,n=[];for(let[r,l]of Object.entries(t))"boolean"==typeof l&&(e=!0),!0===l&&n.push(r.replace(/([A-Z])/g,e=>`-${e.toLowerCase()}`));if(e)for(let e of(g["data-headlessui-state"]=n.join(" "),n))g[`data-${e}`]=""}if(a===r.Fragment&&(Object.keys(p(f)).length>0||Object.keys(p(g)).length>0))if(!(0,r.isValidElement)(v)||Array.isArray(v)&&v.length>1){if(Object.keys(p(f)).length>0)throw Error(['Passing props on "Fragment"!',"",`The current component <${o} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(p(f)).concat(Object.keys(p(g))).map(e=>`  - ${e}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>`  - ${e}`).join(`
`)].join(`
`))}else{var b;let e=v.props,t=null==e?void 0:e.className,n="function"==typeof t?(...e)=>(0,l.x)(t(...e),f.className):(0,l.x)(t,f.className),o=d(v.props,p(m(f,["ref"])));for(let e in g)e in o&&delete g[e];return(0,r.cloneElement)(v,Object.assign({},o,g,h,{ref:i((b=v,r.version.split(".")[0]>="19"?b.props.ref:b.ref),h.ref)},n?{className:n}:{}))}return(0,r.createElement)(a,Object.assign({},m(f,["ref"]),a!==r.Fragment&&h,a!==r.Fragment&&g),v)}function c(...e){return e.every(e=>null==e)?void 0:t=>{for(let n of e)null!=n&&("function"==typeof n?n(t):n.current=t)}}function d(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let r of e)for(let e in r)e.startsWith("on")&&"function"==typeof r[e]?(null!=n[e]||(n[e]=[]),n[e].push(r[e])):t[e]=r[e];if(t.disabled||t["aria-disabled"])for(let e in n)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(e)&&(n[e]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let e in n)Object.assign(t,{[e](t,...r){for(let l of n[e]){if((t instanceof Event||(null==t?void 0:t.nativeEvent)instanceof Event)&&t.defaultPrevented)return;l(t,...r)}}});return t}function f(e){var t;return Object.assign((0,r.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function p(e){let t=Object.assign({},e);for(let e in t)void 0===t[e]&&delete t[e];return t}function m(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}},9704:(e,t,n)=>{n.d(t,{_:()=>r});function r(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}},9857:(e,t,n)=>{n.d(t,{$x:()=>u,El:()=>a,O_:()=>i,Uw:()=>o});var r=n(3210);let l=(0,r.createContext)(null);l.displayName="OpenClosedContext";var o=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(o||{});function i(){return(0,r.useContext)(l)}function a({value:e,children:t}){return r.createElement(l.Provider,{value:e},t)}function u({children:e}){return r.createElement(l.Provider,{value:null},e)}},9923:(e,t,n)=>{n.d(t,{_:()=>a});var r=Object.defineProperty,l=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,o=(e,t,n)=>(l(e,"symbol"!=typeof t?t+"":t,n),n);class i{constructor(){o(this,"current",this.detect()),o(this,"handoffState","pending"),o(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"server"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}}let a=new i}};