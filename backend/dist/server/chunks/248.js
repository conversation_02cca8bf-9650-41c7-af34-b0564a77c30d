"use strict";exports.id=248,exports.ids=[248],exports.modules={1907:(e,s,a)=>{a.d(s,{A:()=>n});var r=a(687),l=a(3210),t=a(9384);let i=(0,l.forwardRef)(({className:e,label:s,error:a,helperText:l,type:i="text",...n},c)=>(0,r.jsxs)("div",{className:"w-full",children:[s&&(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:s}),(0,r.jsx)("input",{type:i,className:(0,t.A)("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",a&&"border-red-300 focus:ring-red-500 focus:border-red-500",e),ref:c,...n}),a&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:a}),l&&!a&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:l})]}));i.displayName="Input";let n=i},3143:(e,s,a)=>{a.d(s,{A:()=>r});let r=(0,a(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4926:(e,s,a)=>{a.d(s,{A:()=>p});var r=a(687),l=a(9384);function t({children:e,className:s}){return(0,r.jsx)("div",{className:"overflow-hidden shadow border border-gray-200 rounded-lg",children:(0,r.jsx)("table",{className:(0,l.A)("min-w-full divide-y divide-gray-300",s),children:e})})}function i({children:e,className:s}){return(0,r.jsx)("thead",{className:(0,l.A)("bg-gray-50",s),children:e})}function n({children:e,className:s}){return(0,r.jsx)("tbody",{className:(0,l.A)("divide-y divide-gray-200 bg-white",s),children:e})}function c({children:e,className:s,onClick:a}){return(0,r.jsx)("tr",{className:(0,l.A)(a&&"cursor-pointer hover:bg-gray-50",s),onClick:a,children:e})}function d({children:e,className:s}){return(0,r.jsx)("th",{scope:"col",className:(0,l.A)("px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",s),children:e})}function o({children:e,className:s}){return(0,r.jsx)("td",{className:(0,l.A)("px-6 py-4 whitespace-nowrap text-sm text-gray-900",s),children:e})}let m=(0,a(2688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);var x=a(4952);let h=[10,25,50,100];function u({pagination:e,onPageChange:s,onPerPageChange:a,className:t}){let{page:i=1,per_page:n=10,total:c=0,total_pages:d=1,has_next:o=!1,has_prev:u=!1}=e||{},p=c>0?(i-1)*n+1:0,g=c>0?Math.min(i*n,c):0;return 0===c?null:(0,r.jsxs)("div",{className:(0,l.A)("flex items-center justify-between bg-white px-4 py-3 sm:px-6",t),children:[(0,r.jsxs)("div",{className:"flex flex-1 justify-between sm:hidden",children:[(0,r.jsx)("button",{onClick:()=>s(i-1),disabled:!u,className:(0,l.A)("relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700",u?"hover:bg-gray-50":"cursor-not-allowed opacity-50"),children:"\xd6nceki"}),(0,r.jsx)("button",{onClick:()=>s(i+1),disabled:!o,className:(0,l.A)("relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700",o?"hover:bg-gray-50":"cursor-not-allowed opacity-50"),children:"Sonraki"})]}),(0,r.jsxs)("div",{className:"hidden sm:flex sm:flex-1 sm:items-center sm:justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,r.jsx)("span",{className:"font-medium",children:p})," - ",(0,r.jsx)("span",{className:"font-medium",children:g})," arası,"," ",(0,r.jsx)("span",{className:"font-medium",children:c})," sonu\xe7tan"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("label",{htmlFor:"per-page",className:"text-sm text-gray-700",children:"Sayfa başına:"}),(0,r.jsx)("select",{id:"per-page",value:n,onChange:e=>a(Number(e.target.value)),className:"rounded-md border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500",children:h.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})]})]}),(0,r.jsx)("div",{children:(0,r.jsxs)("nav",{className:"isolate inline-flex -space-x-px rounded-md shadow-sm","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>s(i-1),disabled:!u,className:(0,l.A)("relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300",u?"hover:bg-gray-50 focus:z-20 focus:outline-offset-0":"cursor-not-allowed opacity-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"\xd6nceki"}),(0,r.jsx)(m,{className:"h-5 w-5","aria-hidden":"true"})]}),(()=>{let e=[],s=[];for(let s=Math.max(2,i-2);s<=Math.min(d-1,i+2);s++)e.push(s);return i-2>2?s.push(1,"..."):s.push(1),s.push(...e),i+2<d-1?s.push("...",d):d>1&&s.push(d),s})().map((e,a)=>(0,r.jsx)("button",{onClick:()=>"number"==typeof e&&s(e),disabled:"..."===e,className:(0,l.A)("relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300",e===i?"z-10 bg-blue-600 text-white focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600":"..."===e?"text-gray-700 cursor-default":"text-gray-900 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"),children:e},a)),(0,r.jsxs)("button",{onClick:()=>s(i+1),disabled:!o,className:(0,l.A)("relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300",o?"hover:bg-gray-50 focus:z-20 focus:outline-offset-0":"cursor-not-allowed opacity-50"),children:[(0,r.jsx)("span",{className:"sr-only",children:"Sonraki"}),(0,r.jsx)(x.A,{className:"h-5 w-5","aria-hidden":"true"})]})]})})]})]})}function p({columns:e,data:s,pagination:a,onPageChange:l,onPerPageChange:m,loading:x=!1,emptyMessage:h="Veri bulunamadı",emptyIcon:p,className:g,useClientPagination:y=!1}){let f=s,j=a;if(y){let e=s.length,r=Math.ceil(e/a.per_page),l=(a.page-1)*a.per_page,t=l+a.per_page;f=s.slice(l,t),j={page:a.page,per_page:a.per_page,total:e,total_pages:r,has_next:a.page<r,has_prev:a.page>1}}return x?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(t,{className:g,children:[(0,r.jsx)(i,{children:(0,r.jsx)(c,{children:e.map(e=>(0,r.jsx)(d,{className:e.className,children:e.header},e.key))})}),(0,r.jsx)(n,{children:Array.from({length:j.per_page}).map((s,a)=>(0,r.jsx)(c,{children:e.map(e=>(0,r.jsx)(o,{className:e.className,children:(0,r.jsx)("div",{className:"animate-pulse bg-gray-200 h-4 rounded"})},e.key))},a))})]}),(0,r.jsx)("div",{className:"flex justify-center py-4",children:(0,r.jsx)("div",{className:"animate-pulse bg-gray-200 h-8 w-64 rounded"})})]}):0===s.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[p&&(0,r.jsx)("div",{className:"flex justify-center mb-4",children:p}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:h}),(0,r.jsx)("p",{className:"text-gray-600",children:"Hen\xfcz veri bulunmuyor."})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(t,{className:g,children:[(0,r.jsx)(i,{children:(0,r.jsx)(c,{children:e.map(e=>(0,r.jsx)(d,{className:e.className,children:e.header},e.key))})}),(0,r.jsx)(n,{children:f.map((s,a)=>(0,r.jsx)(c,{children:e.map(e=>(0,r.jsx)(o,{className:e.className,children:e.render?e.render(s):String(s[e.key]||"")},e.key))},s.id||a))})]}),(0,r.jsx)(u,{pagination:j,onPageChange:l,onPerPageChange:m})]})}},4952:(e,s,a)=>{a.d(s,{A:()=>r});let r=(0,a(2688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7576:(e,s,a)=>{a.d(s,{A:()=>d});var r=a(687),l=a(3210),t=a(3516),i=a(8908),n=a(1860),c=a(2643);function d({isOpen:e,onClose:s,title:a,children:d,size:o="md"}){return(0,r.jsx)(t.e,{appear:!0,show:e,as:l.Fragment,children:(0,r.jsxs)(i.lG,{as:"div",className:"relative z-50",onClose:s,children:[(0,r.jsx)(t.e.Child,{as:l.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,r.jsx)(t.e.Child,{as:l.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,r.jsxs)(i.lG.Panel,{className:`w-full ${{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[o]} transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all`,children:[(0,r.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,r.jsx)(i.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:a}),(0,r.jsx)(c.A,{variant:"secondary",size:"sm",onClick:s,className:"p-2",children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("div",{className:"px-6 py-4",children:d})]})})})})]})})}},9270:(e,s,a)=>{a.d(s,{A:()=>r});let r=(0,a(2688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};