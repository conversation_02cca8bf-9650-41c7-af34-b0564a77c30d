{"version": 1, "files": ["../../../node_modules/client-only/index.js", "../../../node_modules/client-only/package.json", "../../../node_modules/next/dist/compiled/@opentelemetry/api/index.js", "../../../node_modules/next/dist/compiled/@opentelemetry/api/package.json", "../../../node_modules/next/dist/compiled/jsonwebtoken/index.js", "../../../node_modules/next/dist/compiled/jsonwebtoken/package.json", "../../../node_modules/next/dist/compiled/next-server/pages.runtime.prod.js", "../../../node_modules/next/dist/lib/semver-noop.js", "../../../node_modules/next/dist/server/lib/trace/constants.js", "../../../node_modules/next/dist/server/lib/trace/tracer.js", "../../../node_modules/next/dist/shared/lib/is-thenable.js", "../../../node_modules/next/package.json", "../../../node_modules/react-dom/cjs/react-dom-server-legacy.browser.development.js", "../../../node_modules/react-dom/cjs/react-dom-server-legacy.browser.production.js", "../../../node_modules/react-dom/cjs/react-dom-server.browser.development.js", "../../../node_modules/react-dom/cjs/react-dom-server.browser.production.js", "../../../node_modules/react-dom/cjs/react-dom-server.edge.development.js", "../../../node_modules/react-dom/cjs/react-dom-server.edge.production.js", "../../../node_modules/react-dom/cjs/react-dom.development.js", "../../../node_modules/react-dom/cjs/react-dom.production.js", "../../../node_modules/react-dom/index.js", "../../../node_modules/react-dom/package.json", "../../../node_modules/react-dom/server.browser.js", "../../../node_modules/react-dom/server.edge.js", "../../../node_modules/react/cjs/react-jsx-runtime.development.js", "../../../node_modules/react/cjs/react-jsx-runtime.production.js", "../../../node_modules/react/cjs/react.development.js", "../../../node_modules/react/cjs/react.production.js", "../../../node_modules/react/index.js", "../../../node_modules/react/jsx-runtime.js", "../../../node_modules/react/package.json", "../../../node_modules/styled-jsx/dist/index/index.js", "../../../node_modules/styled-jsx/index.js", "../../../node_modules/styled-jsx/package.json", "../../package.json", "../chunks/548.js", "../webpack-runtime.js"]}