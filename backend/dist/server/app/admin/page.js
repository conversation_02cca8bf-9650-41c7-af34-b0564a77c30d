(()=>{var e={};e.id=698,e.ids=[698],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},940:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},981:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(687),a=r(3210),i=r(8383),l=r(4570),n=r(8749),d=r(1312),c=r(940),x=r(9891);let o=(0,r(2688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);function m(){let[e,s]=(0,a.useState)(!0),[r,m]=(0,a.useState)({totalUsers:0,activeUsers:0,adminUsers:0,regularUsers:0}),[p,h]=(0,a.useState)([]);return e?(0,t.jsx)(l.A,{requireAdmin:!0,children:(0,t.jsx)(i.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):(0,t.jsx)(l.A,{requireAdmin:!0,children:(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Admin Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Sistem y\xf6netimi ve kullanıcı istatistikleri"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(d.A,{className:"h-8 w-8 text-blue-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam Kullanıcı"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:r.totalUsers})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(c.A,{className:"h-8 w-8 text-green-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Aktif Kullanıcı"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:r.activeUsers})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(x.A,{className:"h-8 w-8 text-purple-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Admin"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:r.adminUsers})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(o,{className:"h-8 w-8 text-orange-600"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Normal Kullanıcı"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:r.regularUsers})]})]})})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son Kullanıcılar"})}),(0,t.jsx)(n.Wu,{children:0===p.length?(0,t.jsx)("p",{className:"text-gray-500 text-center py-4",children:"Hen\xfcz kullanıcı bulunmuyor"}):(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Kullanıcı Adı"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Rol"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Durum"}),(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Oluşturulma Tarihi"})]})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,t.jsxs)("tr",{children:[(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.username}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,t.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"admin"===e.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"}`,children:"admin"===e.role?"Admin":"Kullanıcı"})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,t.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_active?"Aktif":"Pasif"})}),(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.created_at})]},e.id))})]})})})]})]})})})}r(535)},1132:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/page.tsx","default")},2273:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>c});var t=r(5239),a=r(8088),i=r(8170),l=r.n(i),n=r(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4952:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},5562:(e,s,r)=>{Promise.resolve().then(r.bind(r,1132))},8610:(e,s,r)=>{Promise.resolve().then(r.bind(r,981))},8749:(e,s,r)=>{"use strict";r.d(s,{Wu:()=>d,Zp:()=>l,aR:()=>n});var t=r(687),a=r(3210),i=r(9384);let l=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...r,children:s}));l.displayName="Card";let n=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-b border-gray-200",e),...r,children:s}));n.displayName="CardHeader",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("h3",{ref:a,className:(0,i.A)("text-lg font-semibold text-gray-900",e),...r,children:s})).displayName="CardTitle",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("p",{ref:a,className:(0,i.A)("text-sm text-gray-600 mt-1",e),...r,children:s})).displayName="CardDescription";let d=(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4",e),...r,children:s}));d.displayName="CardContent",(0,a.forwardRef)(({className:e,children:s,...r},a)=>(0,t.jsx)("div",{ref:a,className:(0,i.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...r,children:s})).displayName="CardFooter"},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[169,798,690],()=>r(2273));module.exports=t})();