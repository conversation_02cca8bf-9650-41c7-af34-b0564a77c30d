(()=>{var e={};e.id=133,e.ids=[133],e.modules={495:(e,a,n)=>{"use strict";n.r(a),n.d(a,{default:()=>r});let r=(0,n(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/page.tsx","default")},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2109:(e,a,n)=>{"use strict";n.r(a),n.d(a,{GlobalError:()=>s.a,__next_app__:()=>g,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=n(5239),t=n(8088),i=n(8170),s=n.n(i),o=n(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(a,l);let c={children:["",{children:["admin",{children:["organizations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,495)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/page.tsx"],g={require:n,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/admin/organizations/page",pathname:"/admin/organizations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2413:(e,a,n)=>{"use strict";n.r(a),n.d(a,{default:()=>v});var r=n(687),t=n(3210),i=n(6189),s=n(8383),o=n(4570),l=n(2643),c=n(1907),d=n(7576),g=n(4926),u=n(8233),p=n(3143),h=n(3861),m=n(6474),x=n(9270),y=n(3328);function v(){let e=(0,i.useRouter)(),[a,n]=(0,t.useState)(!0),[v,z]=(0,t.useState)([]),[j,f]=(0,t.useState)(""),[k,_]=(0,t.useState)(""),[b,A]=(0,t.useState)({page:1,per_page:10}),[w,N]=(0,t.useState)(!1),[O,C]=(0,t.useState)(!1),[P,S]=(0,t.useState)(null),[E,q]=(0,t.useState)({name:"",description:""}),[M,$]=(0,t.useState)({name:"",description:"",is_active:!0}),U=async()=>{try{n(!0);let e=await y.h.getMainOrganizations();console.log("data:",e),z(e)}catch(e){console.error("Error loading organizations:",e)}finally{n(!1)}},G=e=>{_(e)},L=async()=>{try{await y.h.createOrganization(E),N(!1),q({name:"",description:""}),U()}catch(e){console.error("Error creating organization:",e),alert("Organizasyon oluşturulurken hata oluştu: "+e.message)}},R=e=>{S(e),$({name:e.name,description:e.description,is_active:e.is_active}),C(!0)},Y=async()=>{if(P)try{await y.h.updateOrganization(P.id,M),C(!1),S(null),$({name:"",description:"",is_active:!0}),U()}catch(e){console.error("Error updating organization:",e),alert("Organizasyon g\xfcncellenirken hata oluştu: "+e.message)}},T=async e=>{if(confirm("Bu organizasyonu silmek istediğinizden emin misiniz?"))try{await y.h.deleteOrganization(e),U()}catch(e){console.error("Error deleting organization:",e),alert("Organizasyon silinirken hata oluştu: "+e.message)}},B=a=>{e.push(`/admin/organizations/${a}`)},D=v.filter(e=>e.name.toLowerCase().includes(j.toLowerCase())||e.description.toLowerCase().includes(j.toLowerCase())),I=[{key:"name",header:"Organizasyon Adı",render:e=>(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name})},{key:"description",header:"A\xe7ıklama",render:e=>(0,r.jsx)("div",{className:"text-gray-600",children:e.description})},{key:"is_active",header:"Durum",render:e=>(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_active?"Aktif":"Pasif"})},{key:"created_at",header:"Oluşturulma Tarihi",render:e=>(0,r.jsx)("div",{className:"text-gray-600",children:e.created_at})},{key:"actions",header:"İşlemler",render:e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2 justify-end",children:[(0,r.jsx)(l.A,{variant:"danger",size:"sm",onClick:()=>T(e.id),children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.A,{variant:"info",size:"sm",onClick:()=>R(e),children:(0,r.jsx)(p.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.A,{variant:"success",size:"sm",onClick:()=>B(e.id),children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})}];return a?(0,r.jsx)(o.A,{requireAdmin:!0,children:(0,r.jsx)(s.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):(0,r.jsx)(o.A,{requireAdmin:!0,children:(0,r.jsx)(s.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Organizasyon Y\xf6netimi"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Sistem organizasyonlarını y\xf6netin"})]}),(0,r.jsxs)(l.A,{onClick:()=>N(!0),children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Yeni Organizasyon"]})]}),(0,r.jsx)("div",{className:"bg-white p-4 rounded-lg shadow-sm border",children:(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-4",children:(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(c.A,{placeholder:"Organizasyon adı veya a\xe7ıklama ile ara...",value:k,onChange:e=>G(e.target.value),className:"flex-1"}),(0,r.jsx)(l.A,{onClick:()=>{f(k),A(e=>({...e,page:1}))},variant:"secondary",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})}),j&&(0,r.jsx)(l.A,{onClick:()=>{f(""),_(""),A(e=>({...e,page:1}))},variant:"secondary",children:"Temizle"})]})})})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border",children:(0,r.jsx)(g.A,{data:D,columns:I,pagination:{page:b.page,per_page:b.per_page,total:D.length,total_pages:Math.ceil(D.length/b.per_page),has_next:b.page<Math.ceil(D.length/b.per_page),has_prev:b.page>1},onPageChange:e=>{A(a=>({...a,page:e}))},onPerPageChange:e=>{A({page:1,per_page:e})},useClientPagination:!0})}),(0,r.jsx)(d.A,{isOpen:w,onClose:()=>N(!1),title:"Yeni Organizasyon Oluştur",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c.A,{label:"Organizasyon Adı",value:E.name,onChange:e=>q({...E,name:e.target.value}),placeholder:"Organizasyon adını girin",required:!0}),(0,r.jsx)(c.A,{label:"A\xe7ıklama",value:E.description,onChange:e=>q({...E,description:e.target.value}),placeholder:"Organizasyon a\xe7ıklamasını girin"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,r.jsx)(l.A,{variant:"secondary",onClick:()=>N(!1),children:"İptal"}),(0,r.jsx)(l.A,{onClick:L,children:"Oluştur"})]})]})}),(0,r.jsx)(d.A,{isOpen:O,onClose:()=>C(!1),title:"Organizasyon D\xfczenle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c.A,{label:"Organizasyon Adı",value:M.name,onChange:e=>$({...M,name:e.target.value}),placeholder:"Organizasyon adını girin"}),(0,r.jsx)(c.A,{label:"A\xe7ıklama",value:M.description,onChange:e=>$({...M,description:e.target.value}),placeholder:"Organizasyon a\xe7ıklamasını girin"}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",id:"is_active",checked:M.is_active,onChange:e=>$({...M,is_active:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"is_active",className:"ml-2 block text-sm text-gray-900",children:"Aktif"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,r.jsx)(l.A,{variant:"secondary",onClick:()=>C(!1),children:"İptal"}),(0,r.jsx)(l.A,{onClick:Y,children:"G\xfcncelle"})]})]})})]})})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3328:(e,a,n)=>{"use strict";n.d(a,{h:()=>t});var r=n(2185);let t={async createOrganization(e){await r.u.post("/organizations",e)},async getAllOrganizations(){try{let e=await r.u.get("/organizations");return e?.data||[]}catch(e){return console.error("Error fetching organizations:",e),[]}},async getOrganizationsPaginated(e){try{let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()}),n=await r.u.get(`/organizations/paginated?${a}`);return n?.data||{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}catch(e){return console.error("Error fetching paginated organizations:",e),{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}},async getOrganizationById(e){try{let a=await r.u.get(`/organizations/${e}`);return a?.data}catch(e){throw console.error("Error fetching organization by id:",e),e}},async updateOrganization(e,a){await r.u.put(`/organizations/${e}`,a)},async deleteOrganization(e){await r.u.delete(`/organizations/${e}`)},async createSubOrganization(e,a){await r.u.post(`/organizations/${e}/sub-organizations`,a)},async getSubOrganizations(e){try{let a=await r.u.get(`/organizations/${e}/sub-organizations`);return a?.data||[]}catch(e){return console.error("Error fetching sub organizations:",e),[]}},async getMainOrganizations(){try{let e=await r.u.get("/organizations/main");return e?.data||[]}catch(e){return console.error("Error fetching main organizations:",e),[]}}}},3861:(e,a,n)=>{"use strict";n.d(a,{A:()=>r});let r=(0,n(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9271:(e,a,n)=>{Promise.resolve().then(n.bind(n,495))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9535:(e,a,n)=>{Promise.resolve().then(n.bind(n,2413))}};var a=require("../../../webpack-runtime.js");a.C(e);var n=e=>a(a.s=e),r=a.X(0,[169,798,71,248,690],()=>n(2109));module.exports=r})();