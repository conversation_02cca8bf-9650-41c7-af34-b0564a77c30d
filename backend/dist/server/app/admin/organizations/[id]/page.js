(()=>{var e={};e.id=345,e.ids=[345],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1071:(e,a,r)=>{Promise.resolve().then(r.bind(r,1077))},1077:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/[id]/page.tsx","default")},1907:(e,a,r)=>{"use strict";r.d(a,{A:()=>l});var t=r(687),s=r(3210),i=r(9384);let n=(0,s.forwardRef)(({className:e,label:a,error:r,helperText:s,type:n="text",...l},o)=>(0,t.jsxs)("div",{className:"w-full",children:[a&&(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:a}),(0,t.jsx)("input",{type:n,className:(0,i.A)("block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",r&&"border-red-300 focus:ring-red-500 focus:border-red-500",e),ref:o,...l}),r&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-600",children:r}),s&&!r&&(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s})]}));n.displayName="Input";let l=n},2527:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>p});var t=r(687),s=r(3210),i=r(6189),n=r(8383),l=r(4570),o=r(2643),d=r(1907),c=r(7576),m=r(8233),x=r(8559),g=r(6474),u=r(1312),h=r(3328);function p(){let e=(0,i.useRouter)(),a=(0,i.useParams)().id,[r,p]=(0,s.useState)(null),[y,j]=(0,s.useState)([]),[b,f]=(0,s.useState)(!0),[v,N]=(0,s.useState)(!1),[z,k]=(0,s.useState)({name:"",description:""});(0,s.useCallback)(async()=>{try{f(!0);let e=await h.h.getOrganizationById(a);p(e)}catch(e){console.error("Error loading organization details:",e)}finally{f(!1)}},[a]);let w=(0,s.useCallback)(async()=>{try{let e=await h.h.getSubOrganizations(a);j(e)}catch(e){console.error("Error loading sub-organizations:",e)}},[a]),A=async()=>{try{await h.h.createSubOrganization(a,{...z,is_main:!1,main_org_id:a}),N(!1),k({name:"",description:""}),w()}catch(e){console.error("Error creating sub-organization:",e)}},_=async e=>{if(window.confirm("Bu alt organizasyonu silmek istediğinizden emin misiniz?"))try{await h.h.deleteOrganization(e),w()}catch(e){console.error("Error deleting sub-organization:",e)}},O=[{key:"name",header:"Organizasyon Adı",render:e=>(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.name})},{key:"description",header:"A\xe7ıklama",render:e=>(0,t.jsx)("div",{className:"text-gray-600",children:e.description||"-"})},{key:"is_active",header:"Durum",render:e=>(0,t.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_active?"Aktif":"Pasif"})},{key:"created_at",header:"Oluşturulma Tarihi",render:e=>(0,t.jsx)("div",{className:"text-gray-600",children:e.created_at})},{key:"actions",header:"İşlemler",render:e=>(0,t.jsx)("div",{className:"flex items-center space-x-2 justify-end",children:(0,t.jsx)(o.A,{variant:"danger",size:"sm",onClick:()=>_(e.id),children:(0,t.jsx)(m.A,{className:"h-4 w-4"})})})}];return b?(0,t.jsx)(l.A,{requireAdmin:!0,children:(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):r?(0,t.jsx)(l.A,{requireAdmin:!0,children:(0,t.jsx)(n.A,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(o.A,{variant:"secondary",onClick:()=>e.back(),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:r.name}),(0,t.jsx)("p",{className:"text-gray-600",children:"Organizasyon Detayları"})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Organizasyon Bilgileri"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Organizasyon Adı"}),(0,t.jsx)("div",{className:"text-gray-900",children:r.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"A\xe7ıklama"}),(0,t.jsx)("div",{className:"text-gray-900",children:r.description||"-"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Durum"}),(0,t.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${r.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:r.is_active?"Aktif":"Pasif"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tip"}),(0,t.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${r.is_main?"bg-blue-100 text-blue-800":"bg-purple-100 text-purple-800"}`,children:r.is_main?"Ana Organizasyon":"Alt Organizasyon"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Oluşturulma Tarihi"}),(0,t.jsx)("div",{className:"text-gray-900",children:r.created_at})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"G\xfcncellenme Tarihi"}),(0,t.jsx)("div",{className:"text-gray-900",children:r.updated_at})]})]})]}),r.is_main&&(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border",children:[(0,t.jsx)("div",{className:"p-6 border-b",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Alt Organizasyonlar"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Bu organizasyona bağlı alt organizasyonlar"})]}),(0,t.jsxs)(o.A,{onClick:()=>N(!0),children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Alt Organizasyon Ekle"]})]})}),(0,t.jsx)("div",{className:"p-6",children:y.length>0?(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,t.jsx)("thead",{className:"bg-gray-50",children:(0,t.jsx)("tr",{children:O.map(e=>(0,t.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e.header},e.key))})}),(0,t.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:y.map(e=>(0,t.jsx)("tr",{children:O.map(a=>(0,t.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:a.render?a.render(e):String(e[a.key]||"")},a.key))},e.id))})]})}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(u.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz alt organizasyon bulunmuyor"})]})})]}),(0,t.jsx)(c.A,{isOpen:v,onClose:()=>N(!1),title:"Alt Organizasyon Ekle",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(d.A,{label:"Organizasyon Adı",value:z.name,onChange:e=>k(a=>({...a,name:e.target.value})),placeholder:"Alt organizasyon adını girin",required:!0}),(0,t.jsx)(d.A,{label:"A\xe7ıklama",value:z.description,onChange:e=>k(a=>({...a,description:e.target.value})),placeholder:"A\xe7ıklama girin (opsiyonel)"}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,t.jsx)(o.A,{variant:"secondary",onClick:()=>N(!1),children:"İptal"}),(0,t.jsx)(o.A,{onClick:A,disabled:!z.name.trim(),children:"Alt Organizasyon Ekle"})]})]})})]})})}):(0,t.jsx)(l.A,{requireAdmin:!0,children:(0,t.jsx)(n.A,{children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"text-lg text-red-600",children:"Organizasyon bulunamadı"})})})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3328:(e,a,r)=>{"use strict";r.d(a,{h:()=>s});var t=r(2185);let s={async createOrganization(e){await t.u.post("/organizations",e)},async getAllOrganizations(){try{let e=await t.u.get("/organizations");return e?.data||[]}catch(e){return console.error("Error fetching organizations:",e),[]}},async getOrganizationsPaginated(e){try{let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()}),r=await t.u.get(`/organizations/paginated?${a}`);return r?.data||{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}catch(e){return console.error("Error fetching paginated organizations:",e),{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}},async getOrganizationById(e){try{let a=await t.u.get(`/organizations/${e}`);return a?.data}catch(e){throw console.error("Error fetching organization by id:",e),e}},async updateOrganization(e,a){await t.u.put(`/organizations/${e}`,a)},async deleteOrganization(e){await t.u.delete(`/organizations/${e}`)},async createSubOrganization(e,a){await t.u.post(`/organizations/${e}/sub-organizations`,a)},async getSubOrganizations(e){try{let a=await t.u.get(`/organizations/${e}/sub-organizations`);return a?.data||[]}catch(e){return console.error("Error fetching sub organizations:",e),[]}},async getMainOrganizations(){try{let e=await t.u.get("/organizations/main");return e?.data||[]}catch(e){return console.error("Error fetching main organizations:",e),[]}}}},3873:e=>{"use strict";e.exports=require("path")},4952:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(2688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},7576:(e,a,r)=>{"use strict";r.d(a,{A:()=>d});var t=r(687),s=r(3210),i=r(3516),n=r(8908),l=r(1860),o=r(2643);function d({isOpen:e,onClose:a,title:r,children:d,size:c="md"}){return(0,t.jsx)(i.e,{appear:!0,show:e,as:s.Fragment,children:(0,t.jsxs)(n.lG,{as:"div",className:"relative z-50",onClose:a,children:[(0,t.jsx)(i.e.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-200",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-25"})}),(0,t.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",children:(0,t.jsx)(i.e.Child,{as:s.Fragment,enter:"ease-out duration-300",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-200",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,t.jsxs)(n.lG.Panel,{className:`w-full ${{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl"}[c]} transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all`,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-6 py-4 border-b border-gray-200",children:[(0,t.jsx)(n.lG.Title,{as:"h3",className:"text-lg font-medium leading-6 text-gray-900",children:r}),(0,t.jsx)(o.A,{variant:"secondary",size:"sm",onClick:a,className:"p-2",children:(0,t.jsx)(l.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"px-6 py-4",children:d})]})})})})]})})}},8559:(e,a,r)=>{"use strict";r.d(a,{A:()=>t});let t=(0,r(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9273:(e,a,r)=>{"use strict";r.r(a),r.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(5239),s=r(8088),i=r(8170),n=r.n(i),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(a,o);let d={children:["",{children:["admin",{children:["organizations",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1077)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/[id]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/organizations/[id]/page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/organizations/[id]/page",pathname:"/admin/organizations/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9319:(e,a,r)=>{Promise.resolve().then(r.bind(r,2527))}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[169,798,71,690],()=>r(9273));module.exports=t})();