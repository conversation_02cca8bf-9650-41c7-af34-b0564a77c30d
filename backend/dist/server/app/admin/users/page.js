(()=>{var e={};e.id=733,e.ids=[733],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2031:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});let s=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/users/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/users/page.tsx","default")},2871:(e,r,a)=>{Promise.resolve().then(a.bind(a,6953))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3039:(e,r,a)=>{Promise.resolve().then(a.bind(a,2031))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3328:(e,r,a)=>{"use strict";a.d(r,{h:()=>n});var s=a(2185);let n={async createOrganization(e){await s.u.post("/organizations",e)},async getAllOrganizations(){try{let e=await s.u.get("/organizations");return e?.data||[]}catch(e){return console.error("Error fetching organizations:",e),[]}},async getOrganizationsPaginated(e){try{let r=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()}),a=await s.u.get(`/organizations/paginated?${r}`);return a?.data||{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}catch(e){return console.error("Error fetching paginated organizations:",e),{data:[],pagination:{page:1,per_page:10,total:0,total_pages:0,has_next:!1,has_prev:!1}}}},async getOrganizationById(e){try{let r=await s.u.get(`/organizations/${e}`);return r?.data}catch(e){throw console.error("Error fetching organization by id:",e),e}},async updateOrganization(e,r){await s.u.put(`/organizations/${e}`,r)},async deleteOrganization(e){await s.u.delete(`/organizations/${e}`)},async createSubOrganization(e,r){await s.u.post(`/organizations/${e}/sub-organizations`,r)},async getSubOrganizations(e){try{let r=await s.u.get(`/organizations/${e}/sub-organizations`);return r?.data||[]}catch(e){return console.error("Error fetching sub organizations:",e),[]}},async getMainOrganizations(){try{let e=await s.u.get("/organizations/main");return e?.data||[]}catch(e){return console.error("Error fetching main organizations:",e),[]}}}},3861:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},6953:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>f});var s=a(687),n=a(3210),t=a(6189),i=a(8383),l=a(4570),o=a(2643),d=a(1907),c=a(7576),u=a(4926),p=a(9891),g=a(8869),m=a(8233),x=a(3143),h=a(3861),y=a(6474),v=a(9270),b=a(535);function f(){let e=(0,t.useRouter)(),[r,a]=(0,n.useState)([]),[f,j]=(0,n.useState)([]),[k,_]=(0,n.useState)(!0),[w,z]=(0,n.useState)(!1),[A,N]=(0,n.useState)(!1),[C,P]=(0,n.useState)(null),[O,K]=(0,n.useState)({page:1,per_page:10}),[S,E]=(0,n.useState)(""),[q,$]=(0,n.useState)(""),[U,R]=(0,n.useState)({username:"",password:"",role:"user",organization_id:""}),[M,Y]=(0,n.useState)({username:"",password:"",role:"user",is_active:!0,organization_id:""}),G=async()=>{try{_(!0);let e=await b.y.getAllUsers();a(e)}catch(e){console.error("Error loading users:",e)}finally{_(!1)}},B=()=>{E(q),K(e=>({...e,page:1}))},D=e=>{$(e)},T=async()=>{try{await b.y.createUser(U),z(!1),R({username:"",password:"",role:"user",organization_id:""}),G()}catch(e){console.error("Error creating user:",e),alert("Kullanıcı oluşturulurken hata oluştu: "+e.message)}},I=e=>{P(e),Y({username:e.username,password:"",role:e.role,is_active:e.is_active,organization_id:e.organization_id}),N(!0)},L=async()=>{if(C)try{let e={...M};e.password||delete e.password,await b.y.updateUser(C.id,e),N(!1),P(null),Y({username:"",password:"",role:"user",is_active:!0,organization_id:""}),G()}catch(e){console.error("Error updating user:",e),alert("Kullanıcı g\xfcncellenirken hata oluştu: "+e.message)}},F=async e=>{if(confirm("Bu kullanıcıyı silmek istediğinizden emin misiniz?"))try{await b.y.deleteUser(e),G()}catch(e){console.error("Error deleting user:",e),alert("Kullanıcı silinirken hata oluştu: "+e.message)}},H=r=>{e.push(`/admin/users/${r}`)},X=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),J=r.filter(e=>{if(!S)return!0;let r=X(S);return X(e.username).includes(r)||X(e.role).includes(r)}),Q=(O.page-1)*O.per_page,V=Q+O.per_page,W=J.slice(Q,V),Z=Math.ceil(J.length/O.per_page),ee=[{key:"username",header:"Kullanıcı Adı",render:e=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0 h-8 w-8",children:(0,s.jsx)("div",{className:`h-8 w-8 rounded-full flex items-center justify-center ${"admin"===e.role?"bg-purple-100":"bg-blue-100"}`,children:"admin"===e.role?(0,s.jsx)(p.A,{className:`h-4 w-4 ${"admin"===e.role?"text-purple-600":"text-blue-600"}`}):(0,s.jsx)(g.A,{className:"h-4 w-4 text-blue-600"})})}),(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.username})})]})},{key:"role",header:"Rol",render:e=>(0,s.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"admin"===e.role?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"}`,children:"admin"===e.role?"Admin":"Kullanıcı"})},{key:"organization",header:"Organizasyon",render:e=>{let r=f.find(r=>r.id===e.organization_id);return(0,s.jsx)("div",{className:"text-sm text-gray-900",children:r?r.name:"Bilinmiyor"})}},{key:"is_active",header:"Durum",render:e=>(0,s.jsx)("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${e.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_active?"Aktif":"Pasif"})},{key:"created_at",header:"Oluşturulma Tarihi",render:e=>e.created_at},{key:"actions",header:"İşlemler",render:e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2 justify-end",children:[(0,s.jsx)(o.A,{variant:"danger",size:"sm",onClick:()=>F(e.id),children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)(o.A,{variant:"info",size:"sm",onClick:()=>I(e),children:(0,s.jsx)(x.A,{className:"h-4 w-4"})}),(0,s.jsx)(o.A,{variant:"success",size:"sm",onClick:()=>H(e.id),children:(0,s.jsx)(h.A,{className:"h-4 w-4"})})]})}];return k?(0,s.jsx)(l.A,{requireAdmin:!0,children:(0,s.jsx)(i.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):(0,s.jsx)(l.A,{requireAdmin:!0,children:(0,s.jsx)(i.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Kullanıcı Y\xf6netimi"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Sistem kullanıcılarını y\xf6netin"})]}),(0,s.jsxs)(o.A,{onClick:()=>z(!0),children:[(0,s.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Yeni Kullanıcı"]})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"Kullanıcı adı veya rol ara...",value:q,onChange:e=>D(e.target.value),onKeyDown:e=>"Enter"===e.key&&B(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)(o.A,{onClick:B,variant:"primary",className:"px-4 py-2",children:"Ara"}),S&&(0,s.jsx)(o.A,{onClick:()=>{E(""),$(""),K(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),S&&(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[J.length,' kullanıcı g\xf6steriliyor • "',S,'" araması']})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:(0,s.jsx)(u.A,{data:W,columns:ee,pagination:{page:O.page,per_page:O.per_page,total:J.length,total_pages:Z,has_next:O.page<Z,has_prev:O.page>1},onPageChange:e=>{K(r=>({...r,page:e}))},onPerPageChange:e=>{K({page:1,per_page:e})}})}),(0,s.jsx)(c.A,{isOpen:w,onClose:()=>z(!1),title:"Yeni Kullanıcı Oluştur",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(d.A,{label:"Kullanıcı Adı",value:U.username,onChange:e=>R({...U,username:e.target.value}),placeholder:"Kullanıcı adını girin",required:!0}),(0,s.jsx)(d.A,{label:"Şifre",type:"password",value:U.password,onChange:e=>R({...U,password:e.target.value}),placeholder:"Şifreyi girin",required:!0}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol"}),(0,s.jsxs)("select",{value:U.role,onChange:e=>R({...U,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"user",children:"Kullanıcı"}),(0,s.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Organizasyon *"}),(0,s.jsxs)("select",{value:U.organization_id,onChange:e=>R({...U,organization_id:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,s.jsx)("option",{value:"",children:"Organizasyon se\xe7in"}),f.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,s.jsx)(o.A,{onClick:()=>z(!1),variant:"secondary",children:"İptal"}),(0,s.jsx)(o.A,{onClick:T,variant:"primary",children:"Oluştur"})]})]})}),(0,s.jsx)(c.A,{isOpen:A,onClose:()=>N(!1),title:"Kullanıcı D\xfczenle",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(d.A,{label:"Kullanıcı Adı",value:M.username,onChange:e=>Y({...M,username:e.target.value}),placeholder:"Kullanıcı adını girin",required:!0}),(0,s.jsx)(d.A,{label:"Yeni Şifre (Boş bırakılırsa değişmez)",type:"password",value:M.password,onChange:e=>Y({...M,password:e.target.value}),placeholder:"Yeni şifreyi girin"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Rol"}),(0,s.jsxs)("select",{value:M.role,onChange:e=>Y({...M,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"user",children:"Kullanıcı"}),(0,s.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Organizasyon"}),(0,s.jsxs)("select",{value:M.organization_id,onChange:e=>Y({...M,organization_id:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"",children:"Organizasyon se\xe7in"}),f.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",id:"is_active",checked:M.is_active,onChange:e=>Y({...M,is_active:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,s.jsx)("label",{htmlFor:"is_active",className:"ml-2 block text-sm text-gray-900",children:"Aktif kullanıcı"})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[(0,s.jsx)(o.A,{onClick:()=>N(!1),variant:"secondary",children:"İptal"}),(0,s.jsx)(o.A,{onClick:L,variant:"primary",children:"G\xfcncelle"})]})]})})]})})})}a(3328)},7401:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=a(5239),n=a(8088),t=a(8170),i=a.n(t),l=a(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(r,o);let d={children:["",{children:["admin",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,2031)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/users/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/admin/users/page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/users/page",pathname:"/admin/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8869:(e,r,a)=>{"use strict";a.d(r,{A:()=>s});let s=(0,a(2688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[169,798,71,248,690],()=>a(7401));module.exports=s})();