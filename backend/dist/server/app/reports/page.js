(()=>{var e={};e.id=22,e.ids=[22],e.modules={228:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(2688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3698:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/debts")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/debts/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/debts/${e}`)).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await s.u.post("/debts",e)},async update(e,t){await s.u.put(`/debts/${e}`,t)},async delete(e){await s.u.delete(`/debts/${e}`)},async pay(e,t){await s.u.post(`/debts/${e}/pay`,t)},getUnpaid:async()=>(await s.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await s.u.get("/debts/paid")).data||[],async getByDateRange(e,t){let a=new URLSearchParams;return e&&a.append("start_date",e),t&&a.append("end_date",t),(await s.u.get(`/debts/filter/date-range?${a.toString()}`)).data||[]},getByPaymentStatus:async e=>(await s.u.get(`/debts/filter/payment-status?is_paid=${e}`)).data||[]}},3873:e=>{"use strict";e.exports=require("path")},4827:(e,t,a)=>{"use strict";a.d(t,{y:()=>c});var s=a(6322),r=a(3698),l=a(7766),n=a(5505),i=a(6899);let c={async getStats(){try{let[e,t,a,c,d]=await Promise.all([s.j.getAll(),r.J.getAll(),l.x.getAll(),n.U.getAll(),i.A.getAll()]),o=a.reduce((e,t)=>e+t.amount,0),m=t.filter(e=>!e.is_paid).reduce((e,t)=>e+t.amount,0),x=new Date,u=d.filter(e=>new Date(e.end_date)>x).length;return{totalProducts:e.length,totalDebts:m,totalSafeAmount:o,unpaidDebtsAmount:m,totalCategories:c.length,activeCampaigns:u}}catch(e){return console.error("Error fetching dashboard stats:",e),{totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}}},async getRecentProducts(e=5){try{return(await s.j.getAll()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent products:",e),[]}},async getRecentDebts(e=5){try{return(await r.J.getUnpaid()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent debts:",e),[]}},async getTopSellingProducts(e=5){try{return(await s.j.getAll()).sort((e,t)=>t.quantity-e.quantity).slice(0,e)}catch(e){return console.error("Error fetching top selling products:",e),[]}},async getRecentActivities(e=5){try{let[t,a]=await Promise.all([this.getRecentProducts(3),this.getRecentDebts(2)]),s=[];return t.forEach(e=>{s.push({id:e.id,type:"product",description:`Yeni \xfcr\xfcn eklendi: ${e.name}`,amount:`₺${e.price}`,time:this.formatTimeAgo(e.created_at)})}),a.forEach(e=>{s.push({id:e.id,type:"debt",description:`Yeni bor\xe7 kaydı: ${e.name} ${e.surname}`,amount:`₺${e.amount}`,time:this.formatTimeAgo(e.created_at)})}),s.sort(()=>Math.random()-.5).slice(0,e)}catch(e){return console.error("Error fetching recent activities:",e),[]}},formatTimeAgo(e){let t=new Date(e),a=Math.floor((new Date().getTime()-t.getTime())/36e5);if(a<1)return"Az \xf6nce";{if(a<24)return`${a} saat \xf6nce`;let e=Math.floor(a/24);return`${e} g\xfcn \xf6nce`}}}},5505:(e,t,a)=>{"use strict";a.d(t,{U:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/categories")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/categories/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/categories/${e}`)).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await s.u.post("/categories",e)},async update(e,t){await s.u.put(`/categories/${e}`,t)},async delete(e){await s.u.delete(`/categories/${e}`)}}},5541:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(2688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},5900:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/reports/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/reports/page.tsx","default")},6322:(e,t,a)=>{"use strict";a.d(t,{j:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/products")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/products/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/products/${e}`)).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let t={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.post("/products",t)},async update(e,t){let a={...t,campaign_id:t.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.put(`/products/${e}`,a)},async delete(e){await s.u.delete(`/products/${e}`)},search:async e=>(await s.u.get(`/products/search?q=${encodeURIComponent(e)}`)).data||[],importFromExcel:async e=>(await s.u.post("/products/import-excel",e)).data}},6517:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var s=a(5239),r=a(8088),l=a(8170),n=a.n(l),i=a(893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(t,c);let d={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5900)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/reports/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/reports/page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6899:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/campaigns")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/campaigns/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/campaigns/${e}`)).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await s.u.post("/campaigns",e)},async update(e,t){await s.u.put(`/campaigns/${e}`,t)},async delete(e){await s.u.delete(`/campaigns/${e}`)},getActive:async()=>(await s.u.get("/campaigns/active")).data||[]}},7002:(e,t,a)=>{Promise.resolve().then(a.bind(a,8799))},7766:(e,t,a)=>{"use strict";a.d(t,{x:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/safes")).data||[],async getById(e){let t=await s.u.get(`/safes/${e}`);if(!t.data)throw Error("Safe not found");return t.data},async create(e){await s.u.post("/safes",e)},async update(e,t){await s.u.put(`/safes/${e}`,t)},async delete(e){await s.u.delete(`/safes/${e}`)},async addMoney(e,t){await s.u.post(`/safes/${e}/add-money`,t)},async withdrawMoney(e,t){await s.u.post(`/safes/${e}/withdraw-money`,t)},getTotalAmount:async()=>(await s.u.get("/safes/total")).total_amount||0}},8749:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>c,Zp:()=>n,aR:()=>i});var s=a(687),r=a(3210),l=a(9384);let n=(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...a,children:t}));n.displayName="Card";let i=(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.A)("px-6 py-4 border-b border-gray-200",e),...a,children:t}));i.displayName="CardHeader",(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("h3",{ref:r,className:(0,l.A)("text-lg font-semibold text-gray-900",e),...a,children:t})).displayName="CardTitle",(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("p",{ref:r,className:(0,l.A)("text-sm text-gray-600 mt-1",e),...a,children:t})).displayName="CardDescription";let c=(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.A)("px-6 py-4",e),...a,children:t}));c.displayName="CardContent",(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,l.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...a,children:t})).displayName="CardFooter"},8799:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>A});var s=a(687),r=a(3210),l=a(646),n=a(8749),i=a(2643),c=a(9080),d=a(5778),o=a(6476),m=a(1312),x=a(3411),u=a(2688);let h=(0,u.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);a(4827);var p=a(228);function g({onDateChange:e,showMonthFilter:t=!0}){let a=new Date().getFullYear(),l=new Date().getMonth()+1,[n,i]=(0,r.useState)(a),[c,d]=(0,r.useState)(l),o=Array.from({length:5},(e,t)=>a-t),m=a=>{i(a),e(a,t?c:0)},x=t=>{d(t),e(n,t)};return(0,s.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-500 mr-2"}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Tarih Filtresi:"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("select",{value:n,onChange:e=>m(parseInt(e.target.value)),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:o.map(e=>(0,s.jsx)("option",{value:e,children:e},e))}),t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"text-gray-500",children:"-"}),(0,s.jsx)("select",{value:c,onChange:e=>x(parseInt(e.target.value)),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[{value:1,label:"Ocak"},{value:2,label:"Şubat"},{value:3,label:"Mart"},{value:4,label:"Nisan"},{value:5,label:"Mayıs"},{value:6,label:"Haziran"},{value:7,label:"Temmuz"},{value:8,label:"Ağustos"},{value:9,label:"Eyl\xfcl"},{value:10,label:"Ekim"},{value:11,label:"Kasım"},{value:12,label:"Aralık"}].map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]})}let y=(0,u.A)("trophy",[["path",{d:"M10 14.66v1.626a2 2 0 0 1-.976 1.696A5 5 0 0 0 7 21.978",key:"1n3hpd"}],["path",{d:"M14 14.66v1.626a2 2 0 0 0 .976 1.696A5 5 0 0 1 17 21.978",key:"rfe1zi"}],["path",{d:"M18 9h1.5a1 1 0 0 0 0-5H18",key:"7xy6bh"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M6 9a6 6 0 0 0 12 0V3a1 1 0 0 0-1-1H7a1 1 0 0 0-1 1z",key:"1mhfuq"}],["path",{d:"M6 9H4.5a1 1 0 0 1 0-5H6",key:"tex48p"}]]);var j=a(5541),f=a(8561),N=a(2185);let v={async getTopSellingProducts(e={}){let t=new URLSearchParams;e.year&&t.append("year",e.year.toString()),e.month&&t.append("month",e.month.toString()),e.limit&&t.append("limit",e.limit.toString());let a=`/sales/reports/top-selling${t.toString()?"?"+t.toString():""}`;return(await N.u.get(a)).data||[]},async getMonthlySales(e){let t=new URLSearchParams;e&&t.append("year",e.toString());let a=`/sales/reports/monthly${t.toString()?"?"+t.toString():""}`;return(await N.u.get(a)).data||[]},async getCategorySales(e,t){let a=new URLSearchParams;e&&a.append("year",e.toString()),t&&a.append("month",t.toString());let s=`/sales/reports/category${a.toString()?"?"+a.toString():""}`;return(await N.u.get(s)).data||[]},async exportMonthlySales(e,t){let a=new URLSearchParams;e&&a.append("year",e.toString()),t&&a.append("month",t.toString());let s=`/sales/reports/export/monthly${a.toString()?"?"+a.toString():""}`;try{let e=await fetch(`http://localhost:5555/api/v1${s}`,{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);let t=e.headers.get("content-disposition"),a="aylik_satislar.xlsx";if(t){let e=t.match(/filename=(.+)/);e&&(a=e[1])}let r=await e.blob(),l=window.URL.createObjectURL(r),n=document.createElement("a");n.href=l,n.download=a,document.body.appendChild(n),n.click(),n.remove(),window.URL.revokeObjectURL(l)}catch(e){throw console.error("Export error:",e),e}}};function b({year:e,month:t,limit:a=5}){let[l,i]=(0,r.useState)([]),[d,o]=(0,r.useState)(!0);return((0,r.useCallback)(async()=>{try{o(!0);let s=await v.getTopSellingProducts({year:e||void 0,month:t||void 0,limit:a});i(s)}catch(e){console.error("Error loading top selling products:",e)}finally{o(!1)}},[e,t,a]),d)?(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(y,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"En \xc7ok Satılan \xdcr\xfcnler"})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Y\xfckleniyor..."})})})]}):(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(y,{className:"h-5 w-5 text-yellow-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"En \xc7ok Satılan \xdcr\xfcnler"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:t?`${t}/${e}`:e})]})}),(0,s.jsx)(n.Wu,{children:l.length>0?(0,s.jsx)("div",{className:"space-y-4",children:l.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${0===t?"bg-yellow-100 text-yellow-600":1===t?"bg-gray-100 text-gray-600":2===t?"bg-orange-100 text-orange-600":"bg-blue-100 text-blue-600"}`,children:t<3?(0,s.jsx)(y,{className:"h-4 w-4"}):(0,s.jsx)("span",{className:"text-sm font-medium",children:t+1})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.product_name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Kod: ",e.product_code]})]})]}),(0,s.jsx)("div",{className:"text-right",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900 flex items-center",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}),e.total_quantity]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Adet"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-green-600 flex items-center",children:[(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}),e.total_revenue.toFixed(2)," TL"]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Gelir"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-blue-600 flex items-center",children:[(0,s.jsx)(f.A,{className:"h-3 w-3 mr-1"}),e.sales_count]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Satış"})]})]})})]},e.product_id))}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(c.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Bu d\xf6nemde satış verisi bulunamadı"})]})})]})}function w({year:e,month:t}){let[a,l]=(0,r.useState)([]),[i,d]=(0,r.useState)(!0);if((0,r.useCallback)(async()=>{try{d(!0);let a=await v.getCategorySales(e||void 0,t||void 0);l(a)}catch(e){console.error("Error loading category sales:",e)}finally{d(!1)}},[e,t]),i)return(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Kategori Bazlı Satışlar"})]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-gray-500",children:"Y\xfckleniyor..."})})})]});let o=Math.max(...a.map(e=>e.total_revenue),1);return(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Kategori Bazlı Satışlar"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:t?`${t}/${e}`:e})]})}),(0,s.jsx)(n.Wu,{children:a.length>0?(0,s.jsx)("div",{className:"max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400",children:(0,s.jsx)("div",{className:"space-y-4 pr-2",children:a.map((e,t)=>{let r=e.total_revenue/o*100;return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,s.jsx)("span",{className:"text-xs font-medium text-blue-600",children:t+1})}),(0,s.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.category_name})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}),e.total_sales," adet"]}),(0,s.jsxs)("div",{className:"flex items-center text-green-600 font-medium",children:[(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}),e.total_revenue.toFixed(2)," TL"]})]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300",style:{width:`${r}%`}})}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,s.jsxs)("span",{children:[e.product_count," \xfcr\xfcn"]}),(0,s.jsxs)("span",{children:["%",(e.total_revenue/a.reduce((e,t)=>e+t.total_revenue,0)*100).toFixed(1)]})]})]},e.category_id)})})}):(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Bu d\xf6nemde kategori satış verisi bulunamadı"})]})})]})}function A(){let[e,t]=(0,r.useState)(!0),[a,u]=(0,r.useState)({totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}),[p,y]=(0,r.useState)([]),[j,f]=(0,r.useState)([]),N=new Date().getFullYear(),A=new Date().getMonth()+1,[S,k]=(0,r.useState)(N),[_,$]=(0,r.useState)(A),P=async()=>{try{t(!0),await v.exportMonthlySales(S,_)}catch(e){console.error("Export error:",e),alert("Excel dosyası indirilemedi: "+e.message)}finally{t(!1)}};return e?(0,s.jsx)(l.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,s.jsx)(l.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Raporlar"}),(0,s.jsx)("p",{className:"text-gray-600",children:"İşletmenizin performans raporlarını g\xf6r\xfcnt\xfcleyin"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam \xdcr\xfcn"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.totalProducts}),(0,s.jsxs)("p",{className:"text-xs text-blue-600 flex items-center",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}),a.totalCategories," kategori"]})]})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-red-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam Bor\xe7"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",a.unpaidDebtsAmount.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-red-600 flex items-center",children:[(0,s.jsx)(d.A,{className:"h-3 w-3 mr-1"}),"\xd6denmemiş bor\xe7lar"]})]})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Kasa Bakiyesi"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",a.totalSafeAmount.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-green-600 flex items-center",children:[(0,s.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Toplam nakit"]})]})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Aktif Kampanya"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.activeCampaigns}),(0,s.jsxs)("p",{className:"text-xs text-purple-600 flex items-center",children:[(0,s.jsx)(m.A,{className:"h-3 w-3 mr-1"}),"Devam eden"]})]})]})})})]}),(0,s.jsx)(g,{onDateChange:(e,t)=>{k(e),$(t)}}),(0,s.jsx)(b,{year:S,month:_,limit:5}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Aylık Satış Trendi"})]}),(0,s.jsxs)(i.A,{onClick:P,disabled:e||0===j.length,className:"flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 text-sm",children:[(0,s.jsx)(h,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:[_,"/",S," Excel İndir"]})]})]})}),(0,s.jsx)(n.Wu,{children:j.length>0?(0,s.jsx)("div",{className:"space-y-4",children:j.slice(0,6).map(e=>{let t=Math.max(...j.map(e=>e.total_revenue),1),a=e.total_revenue/t*100;return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[e.month,"/",e.year]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,s.jsxs)("span",{className:"text-gray-600",children:[e.total_sales," adet"]}),(0,s.jsxs)("span",{className:"text-green-600 font-medium",children:[e.total_revenue.toFixed(2)," TL"]})]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${a}%`}})})]},`${e.year}-${e.month}`)})}):(0,s.jsx)("div",{className:"h-64 flex items-center justify-center bg-gray-50 rounded-lg",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-gray-500",children:"Satış verisi bulunamadı"})]})})})]}),(0,s.jsx)(w,{year:S,month:_})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-6",children:(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son İşlemler"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:p.length>0?p.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.description}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.time})]}),(0,s.jsxs)("div",{className:"text-right",children:[e.amount&&(0,s.jsx)("p",{className:`text-sm font-medium ${e.amount.startsWith("+")?"text-green-600":e.amount.startsWith("-")?"text-red-600":"text-gray-900"}`,children:e.amount}),(0,s.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:e.type})]})]},e.id)):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz işlem bulunmuyor"})})})})]})}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Mali \xd6zet"})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Toplam Kasa"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-green-600",children:["₺",a.totalSafeAmount.toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-green-600",children:"Mevcut nakit"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Toplam Bor\xe7"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-red-600",children:["₺",a.unpaidDebtsAmount.toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-red-600",children:"\xd6denmemiş bor\xe7lar"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:"Net Durum"}),(0,s.jsxs)("p",{className:`text-2xl font-bold ${a.totalSafeAmount-a.unpaidDebtsAmount>=0?"text-green-600":"text-red-600"}`,children:["₺",(a.totalSafeAmount-a.unpaidDebtsAmount).toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-blue-600",children:"Kasa - Bor\xe7"})]})]})})]})]})})}},8850:(e,t,a)=>{Promise.resolve().then(a.bind(a,5900))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[169,798,232],()=>a(6517));module.exports=s})();