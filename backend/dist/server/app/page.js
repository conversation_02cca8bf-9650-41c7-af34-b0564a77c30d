(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1204:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3698:(e,t,a)=>{"use strict";a.d(t,{J:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/debts")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/debts/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/debts/${e}`)).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await s.u.post("/debts",e)},async update(e,t){await s.u.put(`/debts/${e}`,t)},async delete(e){await s.u.delete(`/debts/${e}`)},async pay(e,t){await s.u.post(`/debts/${e}/pay`,t)},getUnpaid:async()=>(await s.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await s.u.get("/debts/paid")).data||[],async getByDateRange(e,t){let a=new URLSearchParams;return e&&a.append("start_date",e),t&&a.append("end_date",t),(await s.u.get(`/debts/filter/date-range?${a.toString()}`)).data||[]},getByPaymentStatus:async e=>(await s.u.get(`/debts/filter/payment-status?is_paid=${e}`)).data||[]}},3873:e=>{"use strict";e.exports=require("path")},4570:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});var s=a(687);a(3210);var r=a(6189),n=a(3213);function i({children:e,requireAdmin:t=!1}){let{isAuthenticated:a,isAdmin:i,isLoading:c}=(0,n.A)();return((0,r.useRouter)(),c)?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:"Y\xfckleniyor..."})]})}):a&&(!t||i)?(0,s.jsx)(s.Fragment,{children:e}):null}},4827:(e,t,a)=>{"use strict";a.d(t,{y:()=>l});var s=a(6322),r=a(3698),n=a(7766),i=a(5505),c=a(6899);let l={async getStats(){try{let[e,t,a,l,d]=await Promise.all([s.j.getAll(),r.J.getAll(),n.x.getAll(),i.U.getAll(),c.A.getAll()]),o=a.reduce((e,t)=>e+t.amount,0),u=t.filter(e=>!e.is_paid).reduce((e,t)=>e+t.amount,0),m=new Date,p=d.filter(e=>new Date(e.end_date)>m).length;return{totalProducts:e.length,totalDebts:u,totalSafeAmount:o,unpaidDebtsAmount:u,totalCategories:l.length,activeCampaigns:p}}catch(e){return console.error("Error fetching dashboard stats:",e),{totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}}},async getRecentProducts(e=5){try{return(await s.j.getAll()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent products:",e),[]}},async getRecentDebts(e=5){try{return(await r.J.getUnpaid()).sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime()).slice(0,e)}catch(e){return console.error("Error fetching recent debts:",e),[]}},async getTopSellingProducts(e=5){try{return(await s.j.getAll()).sort((e,t)=>t.quantity-e.quantity).slice(0,e)}catch(e){return console.error("Error fetching top selling products:",e),[]}},async getRecentActivities(e=5){try{let[t,a]=await Promise.all([this.getRecentProducts(3),this.getRecentDebts(2)]),s=[];return t.forEach(e=>{s.push({id:e.id,type:"product",description:`Yeni \xfcr\xfcn eklendi: ${e.name}`,amount:`₺${e.price}`,time:this.formatTimeAgo(e.created_at)})}),a.forEach(e=>{s.push({id:e.id,type:"debt",description:`Yeni bor\xe7 kaydı: ${e.name} ${e.surname}`,amount:`₺${e.amount}`,time:this.formatTimeAgo(e.created_at)})}),s.sort(()=>Math.random()-.5).slice(0,e)}catch(e){return console.error("Error fetching recent activities:",e),[]}},formatTimeAgo(e){let t=new Date(e),a=Math.floor((new Date().getTime()-t.getTime())/36e5);if(a<1)return"Az \xf6nce";{if(a<24)return`${a} saat \xf6nce`;let e=Math.floor(a/24);return`${e} g\xfcn \xf6nce`}}}},5505:(e,t,a)=>{"use strict";a.d(t,{U:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/categories")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/categories/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/categories/${e}`)).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await s.u.post("/categories",e)},async update(e,t){await s.u.put(`/categories/${e}`,t)},async delete(e){await s.u.delete(`/categories/${e}`)}}},5541:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(2688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},5694:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var s=a(687),r=a(3210),n=a(6189),i=a(3213),c=a(646),l=a(4570),d=a(8749),o=a(9080),u=a(5778),m=a(6476),p=a(5541);function g(){(0,n.useRouter)();let{isAdmin:e}=(0,i.A)(),[t,a]=(0,r.useState)(!0),[g,x]=(0,r.useState)({totalProducts:0,totalDebts:0,totalSafeAmount:0,unpaidDebtsAmount:0,totalCategories:0,activeCampaigns:0}),[h,y]=(0,r.useState)([]),[f,j]=(0,r.useState)([]);return t?(0,s.jsx)(l.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})})}):e?null:(0,s.jsx)(l.A,{children:(0,s.jsx)(c.A,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Dashboard"}),(0,s.jsx)("p",{className:"text-gray-600",children:"İşletme y\xf6netim sisteminize hoş geldiniz"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-blue-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam \xdcr\xfcn"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:g.totalProducts})]})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(u.A,{className:"h-8 w-8 text-red-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Toplam Bor\xe7"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",g.totalDebts.toLocaleString()]})]})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Kasa Bakiyesi"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",g.totalSafeAmount.toLocaleString()]})]})]})})}),(0,s.jsx)(d.Zp,{children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(p.A,{className:"h-8 w-8 text-purple-600"})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Net Bakiye"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₺",(g.totalSafeAmount-g.unpaidDebtsAmount).toLocaleString()]})]})]})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son \xdcr\xfcnler"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:h.length>0?h.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Kod: ",e.product_code]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["₺",e.price.toLocaleString()]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:[e.quantity," adet"]})]})]},e.id)):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz \xfcr\xfcn bulunmuyor"})})})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Son Bor\xe7lar"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:f.length>0?f.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[e.name," ",e.surname]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.phone})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"text-sm font-medium text-red-600",children:["₺",e.amount.toLocaleString()]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:e.is_paid?"\xd6dendi":"\xd6denmemiş"})]})]},e.id)):(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz bor\xe7 bulunmuyor"})})})})]})]})]})})})}a(4827)},6322:(e,t,a)=>{"use strict";a.d(t,{j:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/products")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/products/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/products/${e}`)).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let t={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.post("/products",t)},async update(e,t){let a={...t,campaign_id:t.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.put(`/products/${e}`,a)},async delete(e){await s.u.delete(`/products/${e}`)},search:async e=>(await s.u.get(`/products/search?q=${encodeURIComponent(e)}`)).data||[],importFromExcel:async e=>(await s.u.post("/products/import-excel",e)).data}},6899:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/campaigns")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/campaigns/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/campaigns/${e}`)).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await s.u.post("/campaigns",e)},async update(e,t){await s.u.put(`/campaigns/${e}`,t)},async delete(e){await s.u.delete(`/campaigns/${e}`)},getActive:async()=>(await s.u.get("/campaigns/active")).data||[]}},7766:(e,t,a)=>{"use strict";a.d(t,{x:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/safes")).data||[],async getById(e){let t=await s.u.get(`/safes/${e}`);if(!t.data)throw Error("Safe not found");return t.data},async create(e){await s.u.post("/safes",e)},async update(e,t){await s.u.put(`/safes/${e}`,t)},async delete(e){await s.u.delete(`/safes/${e}`)},async addMoney(e,t){await s.u.post(`/safes/${e}/add-money`,t)},async withdrawMoney(e,t){await s.u.post(`/safes/${e}/withdraw-money`,t)},getTotalAmount:async()=>(await s.u.get("/safes/total")).total_amount||0}},7968:(e,t,a)=>{Promise.resolve().then(a.bind(a,5694))},8217:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d});var s=a(5239),r=a(8088),n=a(8170),i=a.n(n),c=a(893),l={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);a.d(t,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1204)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}],o=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8739:(e,t,a)=>{Promise.resolve().then(a.bind(a,1204))},8749:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>l,Zp:()=>i,aR:()=>c});var s=a(687),r=a(3210),n=a(9384);let i=(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...a,children:t}));i.displayName="Card";let c=(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.A)("px-6 py-4 border-b border-gray-200",e),...a,children:t}));c.displayName="CardHeader",(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("h3",{ref:r,className:(0,n.A)("text-lg font-semibold text-gray-900",e),...a,children:t})).displayName="CardTitle",(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("p",{ref:r,className:(0,n.A)("text-sm text-gray-600 mt-1",e),...a,children:t})).displayName="CardDescription";let l=(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.A)("px-6 py-4",e),...a,children:t}));l.displayName="CardContent",(0,r.forwardRef)(({className:e,children:t,...a},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...a,children:t})).displayName="CardFooter"},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[169,798,232],()=>a(8217));module.exports=s})();