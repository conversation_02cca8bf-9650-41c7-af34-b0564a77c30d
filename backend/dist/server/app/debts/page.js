(()=>{var e={};e.id=923,e.ids=[923],e.modules={111:(e,a,t)=>{Promise.resolve().then(t.bind(t,6617))},359:(e,a,t)=>{Promise.resolve().then(t.bind(t,2239))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2239:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>f});var r=t(687),s=t(3210),n=t(6189),l=t(646),i=t(8749),d=t(2643),o=t(7576),c=t(1907),p=t(4926),u=t(8869),m=t(5778),x=t(3861),g=t(3143),h=t(8233),y=t(6474),b=t(9270),j=t(3698);function f(){let e=(0,n.useRouter)(),[a,t]=(0,s.useState)([]),[f,v]=(0,s.useState)(!0),[N,w]=(0,s.useState)(!1),[A,k]=(0,s.useState)(!1),[C,_]=(0,s.useState)(!1),[P,S]=(0,s.useState)(null),[T,R]=(0,s.useState)(null),[B,M]=(0,s.useState)({name:"",surname:"",phone:"",amount:0}),[z,E]=(0,s.useState)(0),[$,D]=(0,s.useState)("unpaid"),[F,J]=(0,s.useState)(""),[U,q]=(0,s.useState)({page:1,per_page:10}),[G,L]=(0,s.useState)(""),[Y,I]=(0,s.useState)(""),[O,H]=(0,s.useState)(""),K=async()=>{try{let e;if(v(!0),F||G)e=await j.J.getByDateRange(F,G),"all"!==$&&(e=e.filter(e=>"paid"===$?e.is_paid:!e.is_paid)),t(e);else switch($){case"paid":e=await j.J.getPaid(),t(e);break;case"unpaid":e=await j.J.getUnpaid(),t(e);break;default:e=await j.J.getAll(),t(e)}}catch(e){console.error("Error loading debts:",e)}finally{v(!1)}},W=()=>{I(O),q(e=>({...e,page:1}))},Z=e=>{H(e)},X=async()=>{try{await j.J.create(B),w(!1),M({name:"",surname:"",phone:"",amount:0}),K()}catch(e){console.error("Error creating debt:",e)}},Q=async()=>{if(P)try{let e={name:B.name,surname:B.surname,phone:B.phone,amount:B.amount};await j.J.update(P.id,e),k(!1),S(null),K()}catch(e){console.error("Error updating debt:",e)}},V=async e=>{if(confirm("Bu borcu silmek istediğinizden emin misiniz?"))try{await j.J.delete(e),K()}catch(e){console.error("Error deleting debt:",e)}},ee=async()=>{if(T)try{await j.J.pay(T.id,{amount:z}),_(!1),R(null),E(0),K()}catch(e){console.error("Error paying debt:",e)}},ea=e=>{S(e),M({name:e.name,surname:e.surname,phone:e.phone,amount:e.amount}),k(!0)},et=e=>{R(e),E(e.amount),_(!0)},er=e=>{D(e),q({page:1,per_page:U.per_page})},es=e=>new Intl.NumberFormat("tr-TR",{minimumFractionDigits:2,maximumFractionDigits:2}).format(e)+" TL",en=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),el=a.filter(e=>{if(!Y)return!0;let a=en(Y);return en(e.name).includes(a)||en(e.surname).includes(a)||en(e.phone).includes(a)}),ei=[{key:"customer",header:"M\xfcşteri",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900",children:[e.name," ",e.surname]}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.phone})]})]})},{key:"amount",header:"Tutar",render:e=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,r.jsx)("span",{className:"font-medium",children:es(e.amount)})]})},{key:"status",header:"Durum",render:e=>(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${e.is_paid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_paid?"\xd6dendi":"\xd6denmedi"})},{key:"date",header:"Tarih",render:e=>(0,r.jsx)("div",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString("tr-TR")})},{key:"actions",header:"İşlemler",className:"text-right",render:a=>(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[!a.is_paid&&(0,r.jsx)(d.A,{size:"sm",variant:"warning",onClick:()=>et(a),title:"\xd6deme Yap",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(d.A,{size:"sm",variant:"success",onClick:()=>e.push(`/debts/${a.id}`),title:"Detay",children:(0,r.jsx)(x.A,{className:"h-4 w-4"})}),(0,r.jsx)(d.A,{size:"sm",variant:"info",onClick:()=>ea(a),title:"D\xfczenle",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)(d.A,{size:"sm",variant:"danger",onClick:()=>V(a.id),title:"Sil",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})}];return f?(0,r.jsx)(l.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,r.jsx)(l.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Bor\xe7lar"}),(0,r.jsx)("p",{className:"text-gray-600",children:"M\xfcşteri bor\xe7larını takip edin"})]}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(d.A,{onClick:()=>w(!0),children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Yeni Bor\xe7"]})})]}),(0,r.jsxs)("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit",children:[(0,r.jsx)("button",{onClick:()=>er("all"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"all"===$?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"T\xfcm\xfc"}),(0,r.jsx)("button",{onClick:()=>er("unpaid"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"unpaid"===$?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"\xd6denmemiş"}),(0,r.jsx)("button",{onClick:()=>er("paid"),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${"paid"===$?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:"\xd6denmiş"})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Tarih Aralığı Filtresi"})}),(0,r.jsx)(i.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Başlangı\xe7 Tarihi"}),(0,r.jsx)("input",{type:"date",value:F,onChange:e=>J(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Bitiş Tarihi"}),(0,r.jsx)("input",{type:"date",value:G,onChange:e=>L(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{className:"flex space-x-2 pt-6",children:[(0,r.jsx)(d.A,{onClick:()=>{q({page:1,per_page:U.per_page}),K()},variant:"primary",children:"Filtrele"}),(0,r.jsx)(d.A,{onClick:()=>{J(""),L(""),q({page:1,per_page:U.per_page}),K()},variant:"secondary",children:"Temizle"})]})]})})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"M\xfcşteri adı, soyad veya telefon ara...",value:O,onChange:e=>Z(e.target.value),onKeyDown:e=>"Enter"===e.key&&W(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsx)(d.A,{onClick:W,variant:"primary",className:"px-4 py-2",children:"Ara"}),(Y||"unpaid"!==$||F||G)&&(0,r.jsx)(d.A,{onClick:()=>{I(""),H(""),D("unpaid"),J(""),L(""),q(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),Y&&(0,r.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[el.length,' bor\xe7 g\xf6steriliyor • "',Y,'" araması']})]}),(0,r.jsx)(p.A,{columns:ei,data:el,pagination:{page:U.page,per_page:U.per_page,total:el.length,total_pages:Math.ceil(el.length/U.per_page),has_next:U.page<Math.ceil(el.length/U.per_page),has_prev:U.page>1},onPageChange:e=>{q(a=>({...a,page:e}))},onPerPageChange:e=>{q({page:1,per_page:e})},loading:f,emptyMessage:"Hen\xfcz bor\xe7 kaydı bulunmuyor",emptyIcon:(0,r.jsx)(m.A,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0}),(0,r.jsx)(o.A,{isOpen:N,onClose:()=>w(!1),title:"Yeni Bor\xe7 Ekle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c.A,{label:"Ad",value:B.name,onChange:e=>M({...B,name:e.target.value}),placeholder:"M\xfcşteri adı"}),(0,r.jsx)(c.A,{label:"Soyad",value:B.surname,onChange:e=>M({...B,surname:e.target.value}),placeholder:"M\xfcşteri soyadı"}),(0,r.jsx)(c.A,{label:"Telefon",value:B.phone,onChange:e=>M({...B,phone:e.target.value}),placeholder:"0555 123 4567"}),(0,r.jsx)(c.A,{label:"Bor\xe7 Tutarı",type:"number",value:B.amount,onChange:e=>M({...B,amount:parseFloat(e.target.value)||0}),placeholder:"0.00"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(d.A,{variant:"secondary",onClick:()=>w(!1),children:"İptal"}),(0,r.jsx)(d.A,{onClick:X,children:"Bor\xe7 Ekle"})]})]})}),(0,r.jsx)(o.A,{isOpen:A,onClose:()=>k(!1),title:"Bor\xe7 D\xfczenle",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(c.A,{label:"Ad",value:B.name,onChange:e=>M({...B,name:e.target.value}),placeholder:"M\xfcşteri adı"}),(0,r.jsx)(c.A,{label:"Soyad",value:B.surname,onChange:e=>M({...B,surname:e.target.value}),placeholder:"M\xfcşteri soyadı"}),(0,r.jsx)(c.A,{label:"Telefon",value:B.phone,onChange:e=>M({...B,phone:e.target.value}),placeholder:"0555 123 4567"}),(0,r.jsx)(c.A,{label:"Bor\xe7 Tutarı",type:"number",value:B.amount,onChange:e=>M({...B,amount:parseFloat(e.target.value)||0}),placeholder:"0.00"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(d.A,{variant:"secondary",onClick:()=>k(!1),children:"İptal"}),(0,r.jsx)(d.A,{onClick:Q,children:"G\xfcncelle"})]})]})}),(0,r.jsx)(o.A,{isOpen:C,onClose:()=>_(!1),title:"Bor\xe7 \xd6de",children:(0,r.jsx)("div",{className:"space-y-4",children:T&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsxs)("h4",{className:"font-medium text-gray-900",children:[T.name," ",T.surname]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Toplam Bor\xe7: ₺",T.amount]})]}),(0,r.jsx)(c.A,{label:"\xd6deme Tutarı",type:"number",value:z,onChange:e=>E(parseFloat(e.target.value)||0),placeholder:"0.00",helperText:`Maksimum: ₺${T.amount}`}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(d.A,{variant:"secondary",onClick:()=>_(!1),children:"İptal"}),(0,r.jsx)(d.A,{variant:"success",onClick:ee,children:"\xd6deme Yap"})]})]})})})]})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3698:(e,a,t)=>{"use strict";t.d(a,{J:()=>s});var r=t(2185);let s={getAll:async()=>(await r.u.get("/debts")).data||[],async getPaginated(e){let a=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get(`/debts/paginated?${a}`)},async getById(e){try{return(await r.u.get(`/debts/${e}`)).data||null}catch(e){return console.error("Error fetching debt:",e),null}},async create(e){await r.u.post("/debts",e)},async update(e,a){await r.u.put(`/debts/${e}`,a)},async delete(e){await r.u.delete(`/debts/${e}`)},async pay(e,a){await r.u.post(`/debts/${e}/pay`,a)},getUnpaid:async()=>(await r.u.get("/debts/unpaid")).data||[],getPaid:async()=>(await r.u.get("/debts/paid")).data||[],async getByDateRange(e,a){let t=new URLSearchParams;return e&&t.append("start_date",e),a&&t.append("end_date",a),(await r.u.get(`/debts/filter/date-range?${t.toString()}`)).data||[]},getByPaymentStatus:async e=>(await r.u.get(`/debts/filter/payment-status?is_paid=${e}`)).data||[]}},3861:(e,a,t)=>{"use strict";t.d(a,{A:()=>r});let r=(0,t(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},6097:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=t(5239),s=t(8088),n=t(8170),l=t.n(n),i=t(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(a,d);let o={children:["",{children:["debts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6617)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/debts/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/debts/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/debts/page",pathname:"/debts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6617:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/debts/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/debts/page.tsx","default")},8749:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>d,Zp:()=>l,aR:()=>i});var r=t(687),s=t(3210),n=t(9384);let l=(0,s.forwardRef)(({className:e,children:a,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...t,children:a}));l.displayName="Card";let i=(0,s.forwardRef)(({className:e,children:a,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("px-6 py-4 border-b border-gray-200",e),...t,children:a}));i.displayName="CardHeader",(0,s.forwardRef)(({className:e,children:a,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,n.A)("text-lg font-semibold text-gray-900",e),...t,children:a})).displayName="CardTitle",(0,s.forwardRef)(({className:e,children:a,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,n.A)("text-sm text-gray-600 mt-1",e),...t,children:a})).displayName="CardDescription";let d=(0,s.forwardRef)(({className:e,children:a,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("px-6 py-4",e),...t,children:a}));d.displayName="CardContent",(0,s.forwardRef)(({className:e,children:a,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...t,children:a})).displayName="CardFooter"},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[169,798,71,232,248],()=>t(6097));module.exports=r})();