(()=>{var e={};e.id=117,e.ids=[117],e.modules={97:(e,t,a)=>{Promise.resolve().then(a.bind(a,3227))},369:(e,t,a)=>{Promise.resolve().then(a.bind(a,7137))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3196:(e,t,a)=>{"use strict";a.d(t,{T:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/sales")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/sales/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/sales/${e}`)).data||null}catch(e){return console.error("Error fetching sale:",e),null}},async create(e){await s.u.post("/sales",e)},async createMulti(e){await s.u.post("/sales/multi",e)},async update(e,t){await s.u.put(`/sales/${e}`,t)},async delete(e){await s.u.delete(`/sales/${e}`)}}},3227:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},6165:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d});var s=a(5239),r=a(8088),i=a(8170),n=a.n(i),l=a(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(t,c);let d={children:["",{children:["sales",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,3227)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/sales/page",pathname:"/sales",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6322:(e,t,a)=>{"use strict";a.d(t,{j:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/products")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/products/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/products/${e}`)).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let t={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.post("/products",t)},async update(e,t){let a={...t,campaign_id:t.campaign_id||"00000000-0000-0000-0000-000000000000"};await s.u.put(`/products/${e}`,a)},async delete(e){await s.u.delete(`/products/${e}`)},search:async e=>(await s.u.get(`/products/search?q=${encodeURIComponent(e)}`)).data||[],importFromExcel:async e=>(await s.u.post("/products/import-excel",e)).data}},6345:(e,t,a)=>{"use strict";a.d(t,{m:()=>r});var s=a(2185);let r={getAll:async()=>(await s.u.get("/customers")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await s.u.get(`/customers/paginated?${t}`)},async getById(e){try{return(await s.u.get(`/customers/${e}`)).data||null}catch(e){return console.error("Error fetching customer:",e),null}},async create(e){await s.u.post("/customers",e)},async update(e,t){await s.u.put(`/customers/${e}`,t)},async delete(e){await s.u.delete(`/customers/${e}`)},async searchByTC(e){if(!e||e.length<2)return[];try{return(await s.u.get(`/customers/search/${e}`)).data||[]}catch(e){return console.error("Error searching customers:",e),[]}}}},7137:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(687),r=a(3210),i=a(6189),n=a(646),l=a(2643),c=a(7576),d=a(1907),o=a(4926),u=a(8561),m=a(3861),p=a(3143),x=a(8233),h=a(6474),g=a(9270),y=a(3196),_=a(6322);function j({onProductSelect:e,placeholder:t="\xdcr\xfcn adı veya kodu arayın...",disabled:a=!1}){let[i,n]=(0,r.useState)(""),[l,c]=(0,r.useState)([]),[d,o]=(0,r.useState)(!1),[u,m]=(0,r.useState)(!1),p=(0,r.useRef)(null),x=e=>{n(e)},h=t=>{e(t),n(""),c([]),o(!1)};return(0,s.jsxs)("div",{ref:p,className:"relative",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:i,onChange:e=>x(e.target.value),placeholder:t,disabled:a,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"}),u&&(0,s.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"})})]}),d&&l.length>0&&(0,s.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto",children:l.map(e=>(0,s.jsx)("div",{onClick:()=>h(e),className:"px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Kod: ",e.product_code]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.discounted_price<e.price?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{className:"line-through text-gray-400",children:[e.price.toFixed(2)," TL"]}),(0,s.jsxs)("span",{className:"ml-1 text-green-600",children:[e.discounted_price.toFixed(2)," TL"]})]}):(0,s.jsxs)("span",{children:[e.price.toFixed(2)," TL"]})}),(0,s.jsxs)("div",{className:`text-xs ${e.quantity>0?"text-green-600":"text-red-500"}`,children:["Stok: ",e.quantity," ",0===e.quantity&&"(T\xfckendi)"]})]})]})},e.id))}),d&&0===l.length&&""!==i.trim()&&(0,s.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg",children:(0,s.jsx)("div",{className:"px-3 py-2 text-gray-500 text-center",children:"\xdcr\xfcn bulunamadı"})})]})}function v({items:e,products:t,onUpdateItem:a,onRemoveItem:r,totalAmount:i}){let n=(s,r)=>{let i=e[s],n=t.find(e=>e.id===i.product_id);if(!n)return void console.error("Product not found for item:",i.product_id);if(r>n.quantity)return void alert(`Bu \xfcr\xfcn i\xe7in maksimum ${n.quantity} adet satış yapabilirsiniz. Mevcut stok: ${n.quantity}`);r<1&&(r=1);let l=i.unit_price*r;a(s,{quantity:r,total_price:l})},c=(t,s)=>{let r=s*e[t].quantity;a(t,{unit_price:s,total_price:r})},o=(t,s)=>{let r=e[t];a(t,{unit_price:r.quantity>0?s/r.quantity:0,total_price:s})};return 0===e.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Hen\xfcz \xfcr\xfcn eklenmedi. Yukarıdaki arama kutusundan \xfcr\xfcn arayarak ekleyebilirsiniz."}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Se\xe7ilen \xdcr\xfcnler"}),(0,s.jsx)("div",{className:"space-y-3",children:e.map((e,i)=>{let u=t.find(t=>t.id===e.product_id),m=u?.quantity||0;return(0,s.jsx)("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900",children:e.product_name}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["Stok: ",m]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 gap-3 mt-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Miktar"}),(0,s.jsx)(d.A,{type:"number",min:"1",max:m,value:e.quantity,onChange:e=>n(i,parseInt(e.target.value)||1),className:"text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Birim Fiyat"}),(0,s.jsx)(d.A,{type:"number",step:"0.01",min:"0",value:e.unit_price,onChange:e=>c(i,parseFloat(e.target.value)||0),className:"text-sm"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Satıcı İndirimi"}),(0,s.jsx)(d.A,{type:"number",step:"0.01",min:"0",value:e.seller_discount_amount||0,onChange:e=>a(i,{seller_discount_amount:parseFloat(e.target.value)||0}),className:"text-sm",placeholder:"0.00"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Toplam"}),(0,s.jsx)(d.A,{type:"number",step:"0.01",min:"0",value:e.total_price.toFixed(2),onChange:e=>o(i,parseFloat(e.target.value)||0),className:"text-sm"})]})]}),(e.campaign_discount_amount||e.original_price)&&(0,s.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,s.jsx)("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"Kampanya Bilgileri"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-3 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Orijinal Fiyat:"}),(0,s.jsxs)("span",{className:"ml-1 font-medium",children:[(e.original_price||0).toFixed(2)," TL"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Kampanya İndirimi:"}),(0,s.jsxs)("span",{className:"ml-1 font-medium",children:[(e.campaign_discount_amount||0).toFixed(2)," TL"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-blue-700",children:"Kasaya Gidecek:"}),(0,s.jsxs)("span",{className:"ml-1 font-medium text-green-600",children:[(e.total_price+(e.campaign_discount_amount||0)).toFixed(2)," TL"]})]})]})]})]}),(0,s.jsx)(l.A,{variant:"secondary",size:"sm",onClick:()=>r(i),className:"ml-3 text-red-600 hover:text-red-700 hover:bg-red-50",children:"Kaldır"})]})},i)})}),(0,s.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-lg font-medium text-gray-900",children:"Genel Toplam:"}),(0,s.jsxs)("span",{className:"text-xl font-bold text-gray-900",children:[i.toFixed(2)," TL"]})]})})]})}a(6345);var b=a(1312);function f({value:e,onChange:t,onCustomerSelect:a,placeholder:i="TC Kimlik No",label:n="TC Kimlik No",required:l=!1}){let[c,d]=(0,r.useState)([]),[o,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(!1),x=(0,r.useRef)(null),h=(0,r.useRef)(null),g=e=>{t(e.tc),a(e),u(!1)};return(0,s.jsxs)("div",{className:"relative w-full",children:[n&&(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:[n,l&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{ref:x,type:"text",value:e,onChange:e=>{t(e.target.value)},placeholder:i,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",onFocus:()=>{c.length>0&&u(!0)}}),m&&(0,s.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"})})]}),o&&c.length>0&&(0,s.jsx)("div",{ref:h,className:"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto",children:c.map(e=>(0,s.jsx)("div",{onClick:()=>g(e),className:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(b.A,{className:"h-4 w-4 text-gray-400 mr-3"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["TC: ",e.tc,e.phone&&` • Tel: ${e.phone}`,e.address&&` • Adres: ${e.address}`]})]})]})},e.id))})]})}var N=a(3213);function w(){let e=(0,i.useRouter)(),{user:t}=(0,N.A)(),[a,b]=(0,r.useState)([]),[w,k]=(0,r.useState)([]),[A,C]=(0,r.useState)(!0),[S,T]=(0,r.useState)(!1),[q,P]=(0,r.useState)(!1),[$,M]=(0,r.useState)(null),[z,F]=(0,r.useState)({page:1,per_page:10}),[I,E]=(0,r.useState)(""),[R,B]=(0,r.useState)(""),[D,L]=(0,r.useState)({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0}),[O,U]=(0,r.useState)({product_id:"",quantity:1,unit_price:0,total_price:0,customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0}),K=async()=>{try{C(!0);let[e,t]=await Promise.all([y.T.getAll(),_.j.getAll()]);b(e),k(t)}catch(e){console.error("Error loading data:",e)}finally{C(!1)}},G=()=>{E(R),F(e=>({...e,page:1}))},Y=e=>{B(e)},H=e=>{let t=w.find(t=>t.id===e);if(t){let a=t.discounted_price<t.price?t.discounted_price:t.price;U(t=>({...t,product_id:e,unit_price:a,total_price:a*t.quantity}))}},X=e=>{U(t=>({...t,quantity:e,total_price:t.unit_price*e}))},J=(e,t)=>{L(a=>{let s=[...a.items];s[e]={...s[e],...t};let r=s.reduce((e,t)=>e+t.total_price,0);return{...a,items:s,total_amount:r}})},Q=async()=>{if(0===D.items.length)return void alert("En az bir \xfcr\xfcn eklemelisiniz.");try{let e=D.sale_date+" "+new Date().toTimeString().split(" ")[0],a={...D,sale_date:e,organization_id:t?.organization_id||""};await y.T.createMulti(a),T(!1),L({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0}),K()}catch(e){console.error("Error creating multi sale:",e),alert("Satış oluşturulurken hata oluştu: "+e.message)}},V=e=>{M(e),U({product_id:e.product_id,quantity:e.quantity,unit_price:e.unit_price,total_price:e.total_price,customer_name:e.customer_name,customer_phone:e.customer_phone,customer_tc:e.customer_tc,customer_address:e.customer_address,sale_date:e.sale_date.split(" ")[0],is_paid:e.is_paid}),P(!0)},W=async()=>{if($)try{let e=O.sale_date;e.includes(" ")||(e=O.sale_date+" "+new Date().toTimeString().split(" ")[0]);let t={quantity:O.quantity,unit_price:O.unit_price,total_price:O.total_price,customer_name:O.customer_name,customer_phone:O.customer_phone,customer_tc:O.customer_tc,sale_date:e};await y.T.update($.id,t),P(!1),M(null),K()}catch(e){console.error("Error updating sale:",e)}},Z=async e=>{if(confirm("Bu satışı silmek istediğinizden emin misiniz?"))try{await y.T.delete(e),K()}catch(e){console.error("Error deleting sale:",e)}},ee=e=>{if(e.includes(" ")){let[t,a]=e.split(" "),[s,r,i]=t.split("-"),[n,l]=a.split(":");return`${i}.${r}.${s} ${n}:${l}`}let[t,a,s]=e.split("-");return`${s}.${a}.${t}`},et=e=>new Intl.NumberFormat("tr-TR",{style:"currency",currency:"TRY"}).format(e),ea=e=>e.toLowerCase().replace(/ğ/g,"g").replace(/ü/g,"u").replace(/ş/g,"s").replace(/ı/g,"i").replace(/ö/g,"o").replace(/ç/g,"c"),es=a.filter(e=>{if(!I)return!0;let t=ea(I);return ea(e.customer_name).includes(t)||ea(e.customer_phone).includes(t)||ea(e.customer_tc).includes(t)||ea(e.product_name).includes(t)}),er=[{key:"customer",header:"M\xfcşteri",render:e=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.customer_name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.customer_phone})]})]})},{key:"product",header:"\xdcr\xfcn",render:e=>(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.product_name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Adet: ",e.quantity]})]})},{key:"amount",header:"Tutar",render:e=>(0,s.jsx)("div",{className:"font-medium",children:et(e.total_price)})},{key:"payment",header:"\xd6deme",render:e=>(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${e.is_paid?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_paid?"\xd6dendi":"\xd6denmedi"})},{key:"date",header:"Tarih",render:e=>(0,s.jsx)("div",{className:"text-sm",children:ee(e.sale_date)})},{key:"actions",header:"İşlemler",className:"text-right",render:t=>(0,s.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,s.jsx)(l.A,{size:"sm",variant:"success",onClick:()=>e.push(`/sales/${t.id}`),title:"Detay",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.A,{size:"sm",variant:"info",onClick:()=>V(t),title:"D\xfczenle",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})}),(0,s.jsx)(l.A,{size:"sm",variant:"danger",onClick:()=>Z(t.id),title:"Sil",children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})}];return A?(0,s.jsx)(n.A,{children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):(0,s.jsxs)(n.A,{children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Satışlar"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Satış işlemlerini y\xf6netin"})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:(0,s.jsxs)(l.A,{onClick:()=>T(!0),children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Yeni Satış"]})})]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-gray-200",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)("input",{type:"text",placeholder:"M\xfcşteri adı, telefon, TC veya \xfcr\xfcn ara...",value:R,onChange:e=>Y(e.target.value),onKeyDown:e=>"Enter"===e.key&&G(),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,s.jsx)(l.A,{onClick:G,variant:"primary",className:"px-4 py-2",children:"Ara"}),I&&(0,s.jsx)(l.A,{onClick:()=>{E(""),B(""),F(e=>({...e,page:1}))},variant:"secondary",className:"px-4 py-2",children:"Temizle"})]}),I&&(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[es.length,' satış g\xf6steriliyor • "',I,'" araması']})]}),(0,s.jsx)(o.A,{columns:er,data:es,pagination:{page:z.page,per_page:z.per_page,total:es.length,total_pages:Math.ceil(es.length/z.per_page),has_next:z.page<Math.ceil(es.length/z.per_page),has_prev:z.page>1},onPageChange:e=>{F(t=>({...t,page:e}))},onPerPageChange:e=>{F({page:1,per_page:e})},loading:A,emptyMessage:"Hen\xfcz satış kaydı bulunmuyor",emptyIcon:(0,s.jsx)(u.A,{className:"h-12 w-12 text-gray-400"}),useClientPagination:!0})]}),(0,s.jsx)(c.A,{isOpen:S,onClose:()=>{T(!1),L({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0})},title:"Yeni Satış",size:"lg",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\xdcr\xfcn Arama"}),(0,s.jsx)(j,{onProductSelect:e=>{if(e.quantity<=0)return void alert(`"${e.name}" \xfcr\xfcn\xfc stokta bulunmuyor. Mevcut stok: ${e.quantity}`);let t=D.items.findIndex(t=>t.product_id===e.id);if(-1!==t){let a=D.items[t],s=a.quantity+1;return s>e.quantity?void alert(`Bu \xfcr\xfcn i\xe7in maksimum ${e.quantity} adet satış yapabilirsiniz. Şu anda ${a.quantity} adet ekli.`):void J(t,{quantity:s,total_price:a.unit_price*s})}let a=e.discounted_price<e.price?e.discounted_price:e.price,s={product_id:e.id,product_name:e.name,quantity:1,unit_price:a,total_price:a};L(e=>({...e,items:[...e.items,s],total_amount:e.total_amount+a}))}})]}),(0,s.jsx)(v,{items:D.items,products:w,onUpdateItem:J,onRemoveItem:e=>{L(t=>{let a=t.items.filter((t,a)=>a!==e),s=a.reduce((e,t)=>e+t.total_price,0);return{...t,items:a,total_amount:s}})},totalAmount:D.total_amount}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"M\xfcşteri Bilgileri"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)(d.A,{label:"M\xfcşteri Adı",value:D.customer_name,onChange:e=>L(t=>({...t,customer_name:e.target.value}))}),(0,s.jsx)(d.A,{label:"M\xfcşteri Telefonu",value:D.customer_phone,onChange:e=>L(t=>({...t,customer_phone:e.target.value}))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,s.jsx)(f,{label:"M\xfcşteri TC",value:D.customer_tc||"",onChange:e=>L(t=>({...t,customer_tc:e})),onCustomerSelect:e=>{L(t=>({...t,customer_name:e.name,customer_phone:e.phone,customer_tc:e.tc,customer_address:e.address}))}}),(0,s.jsx)(d.A,{label:"Satış Tarihi",type:"date",value:D.sale_date,onChange:e=>L(t=>({...t,sale_date:e.target.value}))})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(d.A,{label:"M\xfcşteri Adresi",value:D.customer_address,onChange:e=>L(t=>({...t,customer_address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."})}),(0,s.jsxs)("div",{className:"space-y-2 mt-4",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"\xd6deme Durumu"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status",checked:D.is_paid,onChange:()=>L(e=>({...e,is_paid:!0})),className:"mr-2"}),"\xd6dendi"]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status",checked:!D.is_paid,onChange:()=>L(e=>({...e,is_paid:!1})),className:"mr-2"}),"\xd6denmedi (Bor\xe7)"]})]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,s.jsx)(l.A,{variant:"secondary",onClick:()=>{T(!1),L({items:[],customer_name:"",customer_phone:"",customer_tc:"",customer_address:"",sale_date:new Date().toISOString().split("T")[0],is_paid:!0,total_amount:0})},className:"flex-1",children:"İptal"}),(0,s.jsx)(l.A,{onClick:Q,className:"flex-1",disabled:0===D.items.length,children:"Satış Oluştur"})]})]})}),(0,s.jsx)(c.A,{isOpen:q,onClose:()=>P(!1),title:"Satışı D\xfczenle",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"\xdcr\xfcn"}),(0,s.jsxs)("select",{value:O.product_id,onChange:e=>H(e.target.value),className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,s.jsx)("option",{value:"",children:"\xdcr\xfcn se\xe7in"}),w.map(e=>(0,s.jsxs)("option",{value:e.id,children:[e.name," (Stok: ",e.quantity,")"]},e.id))]})]}),(0,s.jsx)(d.A,{label:"Miktar",type:"number",min:"1",value:O.quantity,onChange:e=>X(parseInt(e.target.value)||1)}),(0,s.jsx)(d.A,{label:"Birim Fiyat",type:"number",step:"0.01",value:O.unit_price,onChange:e=>U(t=>({...t,unit_price:parseFloat(e.target.value)||0,total_price:(parseFloat(e.target.value)||0)*t.quantity}))}),(0,s.jsx)(d.A,{label:"Toplam Fiyat",type:"number",step:"0.01",value:O.total_price,readOnly:!0}),(0,s.jsx)(d.A,{label:"M\xfcşteri Adı",value:O.customer_name,onChange:e=>U(t=>({...t,customer_name:e.target.value}))}),(0,s.jsx)(d.A,{label:"M\xfcşteri Telefonu",value:O.customer_phone,onChange:e=>U(t=>({...t,customer_phone:e.target.value}))}),(0,s.jsx)(f,{label:"M\xfcşteri TC",value:O.customer_tc||"",onChange:e=>U(t=>({...t,customer_tc:e})),onCustomerSelect:e=>{U(t=>({...t,customer_name:e.name,customer_phone:e.phone,customer_tc:e.tc,customer_address:e.address}))}}),(0,s.jsx)(d.A,{label:"M\xfcşteri Adresi",value:O.customer_address,onChange:e=>U(t=>({...t,customer_address:e.target.value})),placeholder:"M\xfcşteri adresini girin..."}),(0,s.jsx)(d.A,{label:"Satış Tarihi",type:"date",value:O.sale_date,onChange:e=>U(t=>({...t,sale_date:e.target.value}))}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700",children:"\xd6deme Durumu"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status_edit",checked:O.is_paid,onChange:()=>U(e=>({...e,is_paid:!0})),className:"mr-2"}),"\xd6dendi"]}),(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"radio",name:"payment_status_edit",checked:!O.is_paid,onChange:()=>U(e=>({...e,is_paid:!1})),className:"mr-2"}),"\xd6denmedi (Bor\xe7)"]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,s.jsx)(l.A,{variant:"secondary",onClick:()=>P(!1),className:"flex-1",children:"İptal"}),(0,s.jsx)(l.A,{onClick:W,className:"flex-1",disabled:!O.product_id||O.quantity<1,children:"G\xfcncelle"})]})]})})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[169,798,71,232,248],()=>a(6165));module.exports=s})();