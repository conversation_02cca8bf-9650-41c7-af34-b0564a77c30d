(()=>{var e={};e.id=489,e.ids=[489],e.modules={733:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d});var a=t(5239),r=t(8088),n=t(8170),i=t.n(n),l=t(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["sales",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1745)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/[id]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/sales/[id]/page",pathname:"/sales/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1745:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/sales/[id]/page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3196:(e,s,t)=>{"use strict";t.d(s,{T:()=>r});var a=t(2185);let r={getAll:async()=>(await a.u.get("/sales")).data||[],async getPaginated(e){let s=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await a.u.get(`/sales/paginated?${s}`)},async getById(e){try{return(await a.u.get(`/sales/${e}`)).data||null}catch(e){return console.error("Error fetching sale:",e),null}},async create(e){await a.u.post("/sales",e)},async createMulti(e){await a.u.post("/sales/multi",e)},async update(e,s){await a.u.put(`/sales/${e}`,s)},async delete(e){await a.u.delete(`/sales/${e}`)}}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6322:(e,s,t)=>{"use strict";t.d(s,{j:()=>r});var a=t(2185);let r={getAll:async()=>(await a.u.get("/products")).data||[],async getPaginated(e){let s=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await a.u.get(`/products/paginated?${s}`)},async getById(e){try{return(await a.u.get(`/products/${e}`)).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let s={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await a.u.post("/products",s)},async update(e,s){let t={...s,campaign_id:s.campaign_id||"00000000-0000-0000-0000-000000000000"};await a.u.put(`/products/${e}`,t)},async delete(e){await a.u.delete(`/products/${e}`)},search:async e=>(await a.u.get(`/products/search?q=${encodeURIComponent(e)}`)).data||[],importFromExcel:async e=>(await a.u.post("/products/import-excel",e)).data}},6507:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(687),r=t(3210),n=t(6189),i=t(2977),l=t(2643),c=t(1170),d=t(3196),o=t(6322),m=t(4780),u=t(3143),x=t(8233),p=t(9080),g=t(3928),h=t(1312),j=t(5778),y=t(8340),v=t(7992),b=t(228),_=t(3576);function N(){let e=(0,n.useParams)().id,[s,t]=(0,r.useState)(null),[N,f]=(0,r.useState)(null),[w,S]=(0,r.useState)(!0),[k,A]=(0,r.useState)(null);(0,r.useCallback)(async()=>{try{S(!0),A(null);let s=await d.T.getById(e);if(!s)return void A("Satış bulunamadı.");if(t(s),s.product_id){let e=await o.j.getById(s.product_id);e&&f(e)}}catch(e){console.error("Error loading sale:",e),A("Satış bilgileri y\xfcklenirken bir hata oluştu.")}finally{S(!1)}},[e]);let P=()=>s?s.campaign_discount_amount+s.seller_discount_amount:0,T=s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(l.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,a.jsxs)(l.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,a.jsx)(i.Ay,{title:`Satış #${s?.id.slice(-8)||"Detayı"}`,subtitle:"Satış bilgilerini g\xf6r\xfcnt\xfcleyin",loading:w,error:k,backUrl:"/sales",actions:T,children:s&&(0,a.jsxs)(i.A7,{columns:2,children:[(0,a.jsx)(i.JH,{title:"\xdcr\xfcn Bilgileri",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(i.Qn,{label:"\xdcr\xfcn Adı",value:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:s.product_name})]})}),N&&(0,a.jsx)(i.Qn,{label:"\xdcr\xfcn Kodu",value:(0,a.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-2 py-1 rounded",children:N.product_code})}),(0,a.jsx)(i.Qn,{label:"Miktar",value:(0,a.jsxs)("span",{className:"font-semibold text-blue-600",children:[s.quantity," adet"]})}),(0,a.jsx)(i.Qn,{label:"Birim Fiyat",value:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-green-600 mr-1"}),(0,a.jsxs)("span",{children:["₺",s.unit_price.toLocaleString("tr-TR")]})]})})]})}),(0,a.jsx)(i.JH,{title:"M\xfcşteri Bilgileri",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(i.Qn,{label:"Ad Soyad",value:s.customer_name?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsx)("span",{className:"font-medium",children:s.customer_name})]}):(0,a.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})}),(0,a.jsx)(i.Qn,{label:"TC Kimlik",value:s.customer_tc?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,a.jsx)("span",{className:"font-mono text-sm",children:(0,m.NZ)(s.customer_tc)})]}):(0,a.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})}),(0,a.jsx)(i.Qn,{label:"Telefon",value:s.customer_phone?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,a.jsx)("span",{children:(0,m.qH)(s.customer_phone)})]}):(0,a.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})}),(0,a.jsx)(i.Qn,{label:"Adres",value:s.customer_address?(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-red-600 mr-2 mt-0.5 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:s.customer_address})]}):(0,a.jsx)("span",{className:"text-gray-500",children:"Belirtilmemiş"})})]})}),(0,a.jsxs)(i.JH,{title:"Fiyat Hesaplamaları",className:"md:col-span-2",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Orijinal Fiyat"}),(0,a.jsxs)("div",{className:"text-lg font-semibold text-blue-600",children:["₺",s.original_price.toLocaleString("tr-TR")]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-orange-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Kampanya İndirimi"}),(0,a.jsxs)("div",{className:"text-lg font-semibold text-orange-600",children:["₺",s.campaign_discount_amount.toLocaleString("tr-TR")]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-purple-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Satıcı İndirimi"}),(0,a.jsxs)("div",{className:"text-lg font-semibold text-purple-600",children:["₺",s.seller_discount_amount.toLocaleString("tr-TR")]})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-green-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Toplam Tutar"}),(0,a.jsxs)("div",{className:"text-lg font-semibold text-green-600",children:["₺",s.total_price.toLocaleString("tr-TR")]})]})]}),P()>0&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,a.jsx)("strong",{children:"Toplam İndirim:"})," ₺",P().toLocaleString("tr-TR"),"(",s.campaign_discount_amount>0&&`Kampanya: ₺${s.campaign_discount_amount.toLocaleString("tr-TR")}`,s.campaign_discount_amount>0&&s.seller_discount_amount>0&&", ",s.seller_discount_amount>0&&`Satıcı: ₺${s.seller_discount_amount.toLocaleString("tr-TR")}`,")"]})})]}),(0,a.jsx)(i.JH,{title:"Satış Bilgileri",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(i.Qn,{label:"Satış Tarihi",value:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,a.jsx)("span",{children:(0,m.Yq)(s.sale_date)})]})}),(0,a.jsx)(i.Qn,{label:"\xd6deme Durumu",value:s?s.is_paid?(0,a.jsx)(c.E,{variant:"success",children:"\xd6dendi"}):(0,a.jsx)(c.E,{variant:"danger",children:"\xd6denmedi"}):null}),(0,a.jsx)(i.Qn,{label:"Oluşturulma",value:(0,m.Yq)(s.created_at)}),(0,a.jsx)(i.Qn,{label:"Son G\xfcncelleme",value:(0,m.Yq)(s.updated_at)})]})}),(0,a.jsx)(i.JH,{title:"Satış \xd6zeti",children:(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center mb-2",children:[(0,a.jsx)(_.A,{className:"h-5 w-5 text-gray-600 mr-2"}),(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Satış Fişi"})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-700 space-y-1",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"\xdcr\xfcn:"})," ",s.product_name]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Miktar:"})," ",s.quantity," adet"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Birim Fiyat:"})," ₺",s.unit_price.toLocaleString("tr-TR")]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Toplam:"})," ₺",s.total_price.toLocaleString("tr-TR")]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Durum:"})," ",s.is_paid?"\xd6dendi":"\xd6denmedi"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Tarih:"})," ",(0,m.Yq)(s.sale_date)]})]})]})})]})})}},7992:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8913:(e,s,t)=>{Promise.resolve().then(t.bind(t,6507))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9585:(e,s,t)=>{Promise.resolve().then(t.bind(t,1745))}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[169,798,232,145],()=>t(733));module.exports=a})();