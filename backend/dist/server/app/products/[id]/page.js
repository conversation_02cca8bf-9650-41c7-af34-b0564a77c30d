(()=>{var e={};e.id=403,e.ids=[403],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1170:(e,t,a)=>{"use strict";a.d(t,{E:()=>s});var r=a(687);function s({children:e,variant:t="default",className:a=""}){return(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{default:"bg-gray-100 text-gray-800",success:"bg-green-100 text-green-800",danger:"bg-red-100 text-red-800",warning:"bg-yellow-100 text-yellow-800",info:"bg-blue-100 text-blue-800",secondary:"bg-gray-100 text-gray-600"}[t]} ${a}`,children:e})}a(3210)},2977:(e,t,a)=>{"use strict";a.d(t,{A7:()=>p,Ay:()=>o,JH:()=>u,Qn:()=>m});var r=a(687),s=a(6189),n=a(646),i=a(8749),l=a(2643),c=a(8559),d=a(3861);function o({title:e,subtitle:t,loading:a=!1,error:o=null,onBack:u,backUrl:m,children:p,actions:x}){let g=(0,s.useRouter)(),h=()=>{u?u():m?g.push(m):g.back()};return a?(0,r.jsx)(n.A,{children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})})}):o?(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)(l.A,{variant:"secondary",onClick:h,className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Geri"]})}),(0,r.jsx)(i.Zp,{children:(0,r.jsxs)(i.Wu,{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-red-600 text-lg font-medium mb-2",children:"Hata"}),(0,r.jsx)("div",{className:"text-gray-600",children:o})]})})]})}):(0,r.jsx)(n.A,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(l.A,{variant:"secondary",onClick:h,className:"flex items-center",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Geri"]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 text-green-600 mr-2"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e})]}),t&&(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:t})]})]}),x&&(0,r.jsx)("div",{className:"flex items-center space-x-4",children:x})]}),p]})})}function u({title:e,children:t,className:a=""}){return(0,r.jsxs)(i.Zp,{className:a,children:[(0,r.jsx)(i.aR,{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:e})}),(0,r.jsx)(i.Wu,{children:t})]})}function m({label:e,value:t,className:a=""}){return(0,r.jsxs)("div",{className:`flex justify-between py-2 border-b border-gray-100 last:border-b-0 ${a}`,children:[(0,r.jsxs)("span",{className:"text-gray-600 font-medium",children:[e,":"]}),(0,r.jsx)("span",{className:"text-gray-900",children:t})]})}function p({children:e,columns:t=2,className:a=""}){return(0,r.jsx)("div",{className:`grid ${{1:"grid-cols-1",2:"grid-cols-1 md:grid-cols-2",3:"grid-cols-1 md:grid-cols-2 lg:grid-cols-3"}[t]} gap-6 ${a}`,children:e})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3143:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3928:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},4233:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/products/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/products/[id]/page.tsx","default")},4363:(e,t,a)=>{Promise.resolve().then(a.bind(a,4917))},4645:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=a(5239),s=a(8088),n=a(8170),i=a.n(n),l=a(893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(t,c);let d={children:["",{children:["products",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4233)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/products/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,4431)),"/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["/Users/<USER>/Projects/Nocy/business-management/frontend/src/app/products/[id]/page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/products/[id]/page",pathname:"/products/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4780:(e,t,a)=>{"use strict";function r(e){if(!e)return"-";try{let t=new Date(e);if(isNaN(t.getTime()))return e;return t.toLocaleString("tr-TR",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(t){return console.error("Error formatting date:",t),e}}function s(e){if(!e)return"";let t=e.replace(/\D/g,"");return 11===t.length&&t.startsWith("0")?`${t.slice(0,4)} ${t.slice(4,7)} ${t.slice(7,9)} ${t.slice(9,11)}`:10===t.length?`0${t.slice(0,3)} ${t.slice(3,6)} ${t.slice(6,8)} ${t.slice(8,10)}`:e}function n(e){if(!e)return"";let t=e.replace(/\D/g,"");return 11===t.length?`${t.slice(0,3)} ${t.slice(3,6)} ${t.slice(6,9)} ${t.slice(9,11)}`:e}a.d(t,{NZ:()=>n,Yq:()=>r,qH:()=>s})},4917:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var r=a(687),s=a(3210),n=a(6189),i=a(2977),l=a(2643),c=a(1170),d=a(6322),o=a(5505),u=a(6899),m=a(4780),p=a(3143),x=a(8233),g=a(9080),h=a(2688);let y=(0,h.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var j=a(7360),f=a(3928);let v=(0,h.A)("archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);var b=a(798);function N(){let e=(0,n.useParams)().id,[t,a]=(0,s.useState)(null),[h,N]=(0,s.useState)(null),[w,k]=(0,s.useState)(null),[A,_]=(0,s.useState)(!0),[$,P]=(0,s.useState)(null);(0,s.useCallback)(async()=>{try{_(!0),P(null);let t=await d.j.getById(e);if(!t)return void P("\xdcr\xfcn bulunamadı.");if(a(t),t.category_id){let e=await o.U.getById(t.category_id);e&&N(e)}if(t.campaign_id&&"00000000-0000-0000-0000-000000000000"!==t.campaign_id){let e=await u.A.getById(t.campaign_id);e&&k(e)}}catch(e){console.error("Error loading product:",e),P("\xdcr\xfcn bilgileri y\xfcklenirken bir hata oluştu.")}finally{_(!1)}},[e]);let S=t&&t.discounted_price<t.price,R=t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l.A,{variant:"info",onClick:()=>{},className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"D\xfczenle"]}),(0,r.jsxs)(l.A,{variant:"danger",onClick:()=>{},className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Sil"]})]});return(0,r.jsx)(i.Ay,{title:t?.name||"\xdcr\xfcn Detayı",subtitle:"\xdcr\xfcn bilgilerini g\xf6r\xfcnt\xfcleyin",loading:A,error:$,backUrl:"/products",actions:R,children:t&&(0,r.jsxs)(i.A7,{columns:2,children:[(0,r.jsx)(i.JH,{title:"Genel Bilgiler",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(i.Qn,{label:"\xdcr\xfcn Adı",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,r.jsx)("span",{className:"font-medium",children:t.name})]})}),(0,r.jsx)(i.Qn,{label:"\xdcr\xfcn Kodu",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y,{className:"h-4 w-4 text-gray-600 mr-2"}),(0,r.jsx)("span",{className:"font-mono text-sm bg-gray-100 px-2 py-1 rounded",children:t.product_code})]})}),(0,r.jsx)(i.Qn,{label:"Kategori",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 text-purple-600 mr-2"}),(0,r.jsx)("span",{children:h?.name||"Kategori bulunamadı"})]})}),(0,r.jsx)(i.Qn,{label:"Stok Durumu",value:t?0===t.quantity?(0,r.jsx)(c.E,{variant:"danger",children:"Stokta Yok"}):t.quantity<=5?(0,r.jsx)(c.E,{variant:"warning",children:"D\xfcş\xfck Stok"}):(0,r.jsx)(c.E,{variant:"success",children:"Stokta Var"}):null})]})}),(0,r.jsx)(i.JH,{title:"Fiyat ve Stok",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(i.Qn,{label:"Orijinal Fiyat",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 text-green-600 mr-2"}),(0,r.jsxs)("span",{className:S?"line-through text-gray-500":"font-semibold text-green-600",children:["₺",t.price.toLocaleString("tr-TR")]})]})}),S&&(0,r.jsx)(i.Qn,{label:"İndirimli Fiyat",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,r.jsxs)("span",{className:"font-semibold text-red-600",children:["₺",t.discounted_price.toLocaleString("tr-TR")]})]})}),(0,r.jsx)(i.Qn,{label:"Stok Miktarı",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,r.jsxs)("span",{className:"font-semibold",children:[t.quantity," adet"]})]})})]})}),w&&(0,r.jsxs)(i.JH,{title:"Kampanya Bilgileri",className:"md:col-span-2",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsx)(i.Qn,{label:"Kampanya Adı",value:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-orange-600 mr-2"}),(0,r.jsx)("span",{className:"font-medium",children:w.name})]})}),(0,r.jsx)(i.Qn,{label:"İndirim T\xfcr\xfc",value:"percentage"===w.discount_type?"Y\xfczde":"Sabit Tutar"}),(0,r.jsx)(i.Qn,{label:"İndirim Miktarı",value:"percentage"===w.discount_type?`%${w.discount_percent}`:`₺${w.discount_amount.toLocaleString("tr-TR")}`})]}),S&&(0,r.jsx)("div",{className:"mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,r.jsx)("strong",{children:"Kampanya Aktif:"}),' Bu \xfcr\xfcn şu anda "',w.name,'" kampanyası kapsamında indirimli satılmaktadır.']})})]}),(0,r.jsx)(i.JH,{title:"Tarih Bilgileri",className:w?"":"md:col-span-2",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(i.Qn,{label:"Oluşturulma",value:(0,m.Yq)(t.created_at)}),(0,r.jsx)(i.Qn,{label:"Son G\xfcncelleme",value:(0,m.Yq)(t.updated_at)})]})})]})})}},5505:(e,t,a)=>{"use strict";a.d(t,{U:()=>s});var r=a(2185);let s={getAll:async()=>(await r.u.get("/categories")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get(`/categories/paginated?${t}`)},async getById(e){try{return(await r.u.get(`/categories/${e}`)).data||null}catch(e){return console.error("Error fetching category:",e),null}},async create(e){await r.u.post("/categories",e)},async update(e,t){await r.u.put(`/categories/${e}`,t)},async delete(e){await r.u.delete(`/categories/${e}`)}}},6322:(e,t,a)=>{"use strict";a.d(t,{j:()=>s});var r=a(2185);let s={getAll:async()=>(await r.u.get("/products")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get(`/products/paginated?${t}`)},async getById(e){try{return(await r.u.get(`/products/${e}`)).data||null}catch(e){return console.error("Error fetching product:",e),null}},async create(e){let t={...e,campaign_id:e.campaign_id||"00000000-0000-0000-0000-000000000000"};await r.u.post("/products",t)},async update(e,t){let a={...t,campaign_id:t.campaign_id||"00000000-0000-0000-0000-000000000000"};await r.u.put(`/products/${e}`,a)},async delete(e){await r.u.delete(`/products/${e}`)},search:async e=>(await r.u.get(`/products/search?q=${encodeURIComponent(e)}`)).data||[],importFromExcel:async e=>(await r.u.post("/products/import-excel",e)).data}},6899:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(2185);let s={getAll:async()=>(await r.u.get("/campaigns")).data||[],async getPaginated(e){let t=new URLSearchParams({page:e.page.toString(),per_page:e.per_page.toString()});return await r.u.get(`/campaigns/paginated?${t}`)},async getById(e){try{return(await r.u.get(`/campaigns/${e}`)).data||null}catch(e){return console.error("Error fetching campaign:",e),null}},async create(e){await r.u.post("/campaigns",e)},async update(e,t){await r.u.put(`/campaigns/${e}`,t)},async delete(e){await r.u.delete(`/campaigns/${e}`)},getActive:async()=>(await r.u.get("/campaigns/active")).data||[]}},7411:(e,t,a)=>{Promise.resolve().then(a.bind(a,4233))},8233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8559:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8749:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>c,Zp:()=>i,aR:()=>l});var r=a(687),s=a(3210),n=a(9384);let i=(0,s.forwardRef)(({className:e,children:t,...a},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("bg-white rounded-lg border border-gray-200 shadow-sm",e),...a,children:t}));i.displayName="Card";let l=(0,s.forwardRef)(({className:e,children:t,...a},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("px-6 py-4 border-b border-gray-200",e),...a,children:t}));l.displayName="CardHeader",(0,s.forwardRef)(({className:e,children:t,...a},s)=>(0,r.jsx)("h3",{ref:s,className:(0,n.A)("text-lg font-semibold text-gray-900",e),...a,children:t})).displayName="CardTitle",(0,s.forwardRef)(({className:e,children:t,...a},s)=>(0,r.jsx)("p",{ref:s,className:(0,n.A)("text-sm text-gray-600 mt-1",e),...a,children:t})).displayName="CardDescription";let c=(0,s.forwardRef)(({className:e,children:t,...a},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("px-6 py-4",e),...a,children:t}));c.displayName="CardContent",(0,s.forwardRef)(({className:e,children:t,...a},s)=>(0,r.jsx)("div",{ref:s,className:(0,n.A)("px-6 py-4 border-t border-gray-200 bg-gray-50",e),...a,children:t})).displayName="CardFooter"},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[169,798,232],()=>a(4645));module.exports=r})();