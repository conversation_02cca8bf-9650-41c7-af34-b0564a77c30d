app_name: Business Management
host: 
port: 5555
base_url: http://localhost:3000
front_base_url: http://localhost:3000
jwt_secret: secret
jwt_expire: 720 # time in hours
jwt_issuer: business-management
totp_issuer: business-management
jaeger:
  service_name: business-management
  url: localhost:4318

database:
  host: businessdb
  port: 5432
  name: business
  user: business
  password: business
  sslmode: disable

allow_methods:
  - GET
  - POST
  - PUT
  - PATCH
  - DELETE
  - OPTIONS

allow_headers:
  - Content-Type
  - Authorization
  - X-HASH
  - sentry-trace
  - x-mono-auth

allow_origins:
  - http://localhost:5555
  - http://localhost:3000