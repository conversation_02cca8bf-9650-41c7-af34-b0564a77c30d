package dtos

import "github.com/google/uuid"

type CreateCampaignReq struct {
	Name            string    `json:"name" validate:"required"`
	Description     string    `json:"description" validate:"required"`
	StartDate       string    `json:"start_date" validate:"required"`
	EndDate         string    `json:"end_date" validate:"required"`
	DiscountType    string    `json:"discount_type" validate:"required,oneof=percentage amount"`
	DiscountPercent float32   `json:"discount_percent"`
	DiscountAmount  float32   `json:"discount_amount"`
	IsCashback      bool      `json:"is_cashback"` // Default true olacak
	OrganizationID  uuid.UUID `json:"organization_id" validate:"required"`
}

type UpdateCampaignReq struct {
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	StartDate       string    `json:"start_date"`
	EndDate         string    `json:"end_date"`
	DiscountType    string    `json:"discount_type"`
	DiscountPercent *float32  `json:"discount_percent"`
	DiscountAmount  *float32  `json:"discount_amount"`
	IsCashback      *bool     `json:"is_cashback"`
	OrganizationID  uuid.UUID `json:"organization_id"`
}

type CampaignResponse struct {
	ID              uuid.UUID `json:"id"`
	Name            string    `json:"name"`
	Description     string    `json:"description"`
	StartDate       string    `json:"start_date"`
	EndDate         string    `json:"end_date"`
	DiscountType    string    `json:"discount_type"`
	DiscountPercent float32   `json:"discount_percent"`
	DiscountAmount  float32   `json:"discount_amount"`
	IsCashback      bool      `json:"is_cashback"`
	OrganizationID  uuid.UUID `json:"organization_id"`
	CreatedAt       string    `json:"created_at"`
	UpdatedAt       string    `json:"updated_at"`
}
