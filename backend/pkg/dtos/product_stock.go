package dtos

import "github.com/google/uuid"

type CreateProductStockReq struct {
	ProductID         uuid.UUID `json:"product_id" binding:"required"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id" binding:"required"`
	Quantity          int       `json:"quantity" binding:"required,min=0"`
}

type UpdateProductStockReq struct {
	Quantity int `json:"quantity" binding:"min=0"`
}

type ProductStockResponse struct {
	ID                uuid.UUID `json:"id"`
	ProductID         uuid.UUID `json:"product_id"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
	Quantity          int       `json:"quantity"`
	CreatedAt         string    `json:"created_at"`
	UpdatedAt         string    `json:"updated_at"`
}

type AddStockReq struct {
	ProductID         uuid.UUID `json:"product_id" binding:"required"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id" binding:"required"`
	Quantity          int       `json:"quantity" binding:"required,min=1"`
}

type ProductStockSummaryResponse struct {
	ProductID     uuid.UUID `json:"product_id"`
	ProductName   string    `json:"product_name"`
	ProductCode   string    `json:"product_code"`
	TotalQuantity int       `json:"total_quantity"`
	StockDetails  []ProductStockResponse `json:"stock_details"`
}
