package dtos

import "github.com/google/uuid"

type CreateDebtReq struct {
	Name              string    `json:"name" validate:"required"`
	Surname           string    `json:"surname" validate:"required"`
	Phone             string    `json:"phone" validate:"required"`
	Amount            float32   `json:"amount" validate:"required,gt=0"`
	OrganizationID    uuid.UUID `json:"organization_id" validate:"required"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id" validate:"required"`
}

type UpdateDebtReq struct {
	Name              string    `json:"name"`
	Surname           string    `json:"surname"`
	Phone             string    `json:"phone"`
	Amount            float32   `json:"amount"`
	OrganizationID    uuid.UUID `json:"organization_id"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
}

type PayDebtReq struct {
	Amount float32 `json:"amount" validate:"required,gt=0"`
}

type DebtResponse struct {
	ID                uuid.UUID `json:"id"`
	Name              string    `json:"name"`
	Surname           string    `json:"surname"`
	Phone             string    `json:"phone"`
	Amount            float32   `json:"amount"`
	IsPaid            bool      `json:"is_paid"`
	OrganizationID    uuid.UUID `json:"organization_id"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id"`
	CreatedAt         string    `json:"created_at"`
	UpdatedAt         string    `json:"updated_at"`
}
