package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type Sale struct {
	Base
	ProductID              uuid.UUID `json:"product_id" gorm:"type:uuid;not null"`
	Quantity               int       `json:"quantity" gorm:"not null"`
	UnitPrice              float32   `json:"unit_price" gorm:"not null"`
	TotalPrice             float32   `json:"total_price" gorm:"not null"`
	CustomerName           string    `json:"customer_name"`
	CustomerPhone          string    `json:"customer_phone"`
	CustomerTC             string    `json:"customer_tc"`
	CustomerAddress        string    `json:"customer_address"`
	SaleDate               string    `json:"sale_date"`
	IsPaid                 bool      `json:"is_paid" gorm:"default:false"`
	CampaignDiscountAmount float32   `json:"campaign_discount_amount" gorm:"default:0"` // Kampanya indirimi
	SellerDiscountAmount   float32   `json:"seller_discount_amount" gorm:"default:0"`   // Satıcı indirimi
	OriginalPrice          float32   `json:"original_price" gorm:"default:0"`           // Orijinal ürün fiyatı
	OrganizationID         uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
	SubOrganizationID      uuid.UUID `json:"sub_organization_id" gorm:"type:uuid;not null"`
}

func (s *Sale) Mapper(req dtos.CreateSaleReq) {
	s.ProductID = req.ProductID
	s.Quantity = req.Quantity
	s.UnitPrice = req.UnitPrice
	s.TotalPrice = req.TotalPrice
	s.CustomerName = req.CustomerName
	s.CustomerPhone = req.CustomerPhone
	s.CustomerTC = req.CustomerTC
	s.CustomerAddress = req.CustomerAddress
	s.SaleDate = req.SaleDate
	s.IsPaid = req.IsPaid
	s.CampaignDiscountAmount = req.CampaignDiscountAmount
	s.SellerDiscountAmount = req.SellerDiscountAmount
	s.OriginalPrice = req.OriginalPrice
	s.OrganizationID = req.OrganizationID
	s.SubOrganizationID = req.SubOrganizationID
}

func (s *Sale) UpdateMapper(req dtos.UpdateSaleReq) {
	if req.Quantity > 0 {
		s.Quantity = req.Quantity
	}
	if req.UnitPrice > 0 {
		s.UnitPrice = req.UnitPrice
	}
	if req.TotalPrice > 0 {
		s.TotalPrice = req.TotalPrice
	}
	if req.CustomerName != "" {
		s.CustomerName = req.CustomerName
	}
	if req.CustomerPhone != "" {
		s.CustomerPhone = req.CustomerPhone
	}
	if req.CustomerTC != "" {
		s.CustomerTC = req.CustomerTC
	}
	if req.CustomerAddress != "" {
		s.CustomerAddress = req.CustomerAddress
	}
	if req.SaleDate != "" {
		s.SaleDate = req.SaleDate
	}
	if req.IsPaid != nil {
		s.IsPaid = *req.IsPaid
	}
	if req.CampaignDiscountAmount != nil {
		s.CampaignDiscountAmount = *req.CampaignDiscountAmount
	}
	if req.SellerDiscountAmount != nil {
		s.SellerDiscountAmount = *req.SellerDiscountAmount
	}
	if req.OriginalPrice != nil {
		s.OriginalPrice = *req.OriginalPrice
	}
}

func (s *Sale) ToResponse() dtos.SaleResponse {
	return dtos.SaleResponse{
		ID:                     s.ID,
		ProductID:              s.ProductID,
		Quantity:               s.Quantity,
		UnitPrice:              s.UnitPrice,
		TotalPrice:             s.TotalPrice,
		CustomerName:           s.CustomerName,
		CustomerPhone:          s.CustomerPhone,
		CustomerTC:             s.CustomerTC,
		CustomerAddress:        s.CustomerAddress,
		SaleDate:               s.SaleDate,
		IsPaid:                 s.IsPaid,
		CampaignDiscountAmount: s.CampaignDiscountAmount,
		SellerDiscountAmount:   s.SellerDiscountAmount,
		OriginalPrice:          s.OriginalPrice,
		OrganizationID:         s.OrganizationID,
		SubOrganizationID:      s.SubOrganizationID,
		CreatedAt:              s.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:              s.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
