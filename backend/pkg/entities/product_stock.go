package entities

import (
	"business-mamagement/pkg/dtos"
	"errors"

	"github.com/google/uuid"
)

type ProductStock struct {
	Base
	ProductID         uuid.UUID `json:"product_id" gorm:"type:uuid;not null"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id" gorm:"type:uuid;not null"`
	Quantity          int       `json:"quantity" gorm:"default:0"`

	// Foreign key relationships
	Product         Product      `json:"product" gorm:"foreignKey:ProductID"`
	SubOrganization Organization `json:"sub_organization" gorm:"foreignKey:SubOrganizationID"`
}

func (ps *ProductStock) Mapper(req dtos.CreateProductStockReq) {
	ps.ProductID = req.ProductID
	ps.SubOrganizationID = req.SubOrganizationID
	ps.Quantity = req.Quantity
}

func (ps *ProductStock) UpdateMapper(req dtos.UpdateProductStockReq) {
	if req.Quantity >= 0 {
		ps.Quantity = req.Quantity
	}
}

func (ps *ProductStock) AddStock(quantity int) {
	ps.Quantity += quantity
}

func (ps *ProductStock) ReduceStock(quantity int) error {
	if ps.Quantity < quantity {
		return errors.New("insufficient stock")
	}
	ps.Quantity -= quantity
	return nil
}

func (ps *ProductStock) ToResponse() dtos.ProductStockResponse {
	return dtos.ProductStockResponse{
		ID:                ps.ID,
		ProductID:         ps.ProductID,
		SubOrganizationID: ps.SubOrganizationID,
		Quantity:          ps.Quantity,
		CreatedAt:         ps.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:         ps.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
