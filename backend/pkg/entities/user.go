package entities

import (
	"business-mamagement/pkg/dtos"

	"github.com/google/uuid"
)

type User struct {
	Base
	Username          string    `json:"username" gorm:"unique;not null"`
	Password          string    `json:"password" gorm:"not null"`
	Role              string    `json:"role" gorm:"default:user"` // "admin" or "user"
	IsActive          bool      `json:"is_active" gorm:"default:true"`
	OrganizationID    uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
	SubOrganizationID uuid.UUID `json:"sub_organization_id" gorm:"type:uuid"`
}

func (u *User) Mapper(req dtos.CreateUserReq) {
	u.Username = req.Username
	u.Password = req.Password
	u.Role = req.Role
	u.IsActive = true
	u.OrganizationID = req.OrganizationID
	u.SubOrganizationID = req.SubOrganizationID
}

func (u *User) UpdateMapper(req dtos.UpdateUserReq) {
	if req.Username != "" {
		u.Username = req.Username
	}
	if req.Password != "" {
		u.Password = req.Password
	}
	if req.Role != "" {
		u.Role = req.Role
	}
	if req.IsActive != nil {
		u.IsActive = *req.IsActive
	}
	if req.OrganizationID != uuid.Nil {
		u.OrganizationID = req.OrganizationID
	}
	if req.SubOrganizationID != uuid.Nil {
		u.SubOrganizationID = req.SubOrganizationID
	}
}

func (u *User) ToResponse() dtos.UserResponse {
	return dtos.UserResponse{
		ID:                u.ID,
		Username:          u.Username,
		Role:              u.Role,
		IsActive:          u.IsActive,
		OrganizationID:    u.OrganizationID,
		SubOrganizationID: u.SubOrganizationID,
		CreatedAt:         u.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:         u.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}
