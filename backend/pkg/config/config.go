package config

import (
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

type Config struct {
	AppName      string `yaml:"app_name"`
	Host         string `yaml:"host"`
	Port         string `yaml:"port"`
	BaseUrl      string `yaml:"base_url"`
	FrontBaseUrl string `yaml:"front_base_url"`
	JwtSecret    string `yaml:"jwt_secret"`
	JwtExpire    int    `yaml:"jwt_expire"`
	JwtIssuer    string `yaml:"jwt_issuer"`
	TotpIssuer   string `yaml:"totp_issuer"`
	J<PERSON>ger       struct {
		ServiceName string `yaml:"service_name"`
		Url         string `yaml:"url"`
	}
	AllowMethods []string `yaml:"allow_methods"`
	AllowHeaders []string `yaml:"allow_headers"`
	AllowOrigins []string `yaml:"allow_origins"`
	Database     struct {
		Host     string `yaml:"host"`
		Port     string `yaml:"port"`
		User     string `yaml:"user"`
		Password string `yaml:"password"`
		Name     string `yaml:"name"`
		SslMode  string `yaml:"sslmode"`
		ReadHost string `yaml:"read_host"`
	} `yaml:"database"`
}

var configs *Config

func ReadValue() *Config {
	if configs != nil {
		return configs
	}

	filename, err := filepath.Abs("./config.yaml")
	if err != nil {
		log.Fatal("error getting absolute path for config.yaml: ", err)
	}

	cleanedDst := filepath.Clean(filename)
	log.Printf("Loading config from: %s", cleanedDst)

	yamlFile, err := os.ReadFile(cleanedDst)
	if err != nil {
		log.Fatal("error reading config.yaml file: ", err)
	}

	err = yaml.Unmarshal(yamlFile, &configs)
	if err != nil {
		log.Fatal("error parsing config.yaml: ", err)
	}

	if configs == nil {
		log.Fatal("config is nil after parsing")
	}

	// Override with environment variables if they exist
	if dbHost := os.Getenv("DB_HOST"); dbHost != "" {
		configs.Database.Host = dbHost
	}
	if dbPort := os.Getenv("DB_PORT"); dbPort != "" {
		configs.Database.Port = dbPort
	}
	if dbUser := os.Getenv("DB_USER"); dbUser != "" {
		configs.Database.User = dbUser
	}
	if dbPassword := os.Getenv("DB_PASSWORD"); dbPassword != "" {
		configs.Database.Password = dbPassword
	}
	if dbName := os.Getenv("DB_NAME"); dbName != "" {
		configs.Database.Name = dbName
	}

	return configs
}
