package safe

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"context"
	"errors"

	"github.com/google/uuid"
)

type Service interface {
	CreateSafe(ctx context.Context, req dtos.CreateSafeReq) error
	GetAllSafes(ctx context.Context) ([]dtos.SafeResponse, error)
	GetSafeByID(ctx context.Context, id uuid.UUID) (*dtos.SafeResponse, error)
	UpdateSafe(ctx context.Context, id uuid.UUID, req dtos.UpdateSafeReq) error
	DeleteSafe(ctx context.Context, id uuid.UUID) error
	AddMoney(ctx context.Context, id uuid.UUID, req dtos.AddMoneyReq) error
	WithdrawMoney(ctx context.Context, id uuid.UUID, req dtos.WithdrawMoneyReq) error
	GetTotalAmount(ctx context.Context) (float32, error)
	GetFirstSafe(ctx context.Context) (*dtos.SafeResponse, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateSafe(ctx context.Context, req dtos.CreateSafeReq) error {
	// Set organization and sub-organization IDs from context
	organizationID := state.CurrentUserOrganization(ctx)
	if organizationID == uuid.Nil {
		return errors.New("organization ID not found in context")
	}
	req.OrganizationID = organizationID

	subOrganizationID := state.CurrentUserSubOrganization(ctx)
	if subOrganizationID == uuid.Nil {
		return errors.New("sub-organization ID not found in context")
	}
	req.SubOrganizationID = subOrganizationID

	return s.repo.CreateSafe(ctx, req)
}

func (s *service) GetAllSafes(ctx context.Context) ([]dtos.SafeResponse, error) {
	safes, err := s.repo.GetAllSafes(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.SafeResponse
	for _, safe := range safes {
		responses = append(responses, safe.ToResponse())
	}
	return responses, nil
}

func (s *service) GetSafeByID(ctx context.Context, id uuid.UUID) (*dtos.SafeResponse, error) {
	safe, err := s.repo.GetSafeByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if safe == nil {
		return nil, nil
	}

	response := safe.ToResponse()
	return &response, nil
}

func (s *service) UpdateSafe(ctx context.Context, id uuid.UUID, req dtos.UpdateSafeReq) error {
	return s.repo.UpdateSafe(ctx, id, req)
}

func (s *service) DeleteSafe(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteSafe(ctx, id)
}

func (s *service) AddMoney(ctx context.Context, id uuid.UUID, req dtos.AddMoneyReq) error {
	return s.repo.AddMoney(ctx, id, req.Amount)
}

func (s *service) WithdrawMoney(ctx context.Context, id uuid.UUID, req dtos.WithdrawMoneyReq) error {
	return s.repo.WithdrawMoney(ctx, id, req.Amount)
}

func (s *service) GetTotalAmount(ctx context.Context) (float32, error) {
	return s.repo.GetTotalAmount(ctx)
}

func (s *service) GetFirstSafe(ctx context.Context) (*dtos.SafeResponse, error) {
	safe, err := s.repo.GetFirstSafe(ctx)
	if err != nil {
		return nil, err
	}
	if safe == nil {
		return nil, nil
	}

	response := safe.ToResponse()
	return &response, nil
}
