package safe

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateSafe(ctx context.Context, req dtos.CreateSafeReq) error
	GetAllSafes(ctx context.Context) ([]entities.Safe, error)
	GetSafeByID(ctx context.Context, id uuid.UUID) (*entities.Safe, error)
	UpdateSafe(ctx context.Context, id uuid.UUID, req dtos.UpdateSafeReq) error
	DeleteSafe(ctx context.Context, id uuid.UUID) error
	AddMoney(ctx context.Context, id uuid.UUID, amount float32) error
	WithdrawMoney(ctx context.Context, id uuid.UUID, amount float32) error
	GetTotalAmount(ctx context.Context) (float32, error)
	GetFirstSafe(ctx context.Context) (*entities.Safe, error)
	HasAnySafe(ctx context.Context) (bool, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateSafe(ctx context.Context, req dtos.CreateSafeReq) error {
	// Check if a safe already exists
	hasSafe, err := r.HasAnySafe(ctx)
	if err != nil {
		return err
	}
	if hasSafe {
		return errors.New("a safe already exists, only one safe is allowed")
	}

	var safe entities.Safe
	safe.Mapper(req)
	return r.db.WithContext(ctx).Create(&safe).Error
}

func (r *repository) GetAllSafes(ctx context.Context) ([]entities.Safe, error) {
	var safes []entities.Safe

	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return nil, errors.New("sub-organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND sub_organization_id = ?", subOrganizationID).Find(&safes).Error
	return safes, err
}

func (r *repository) GetSafeByID(ctx context.Context, id uuid.UUID) (*entities.Safe, error) {
	var safe entities.Safe

	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return nil, errors.New("sub-organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL AND sub_organization_id = ?", id, subOrganizationID).First(&safe).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &safe, nil
}

func (r *repository) UpdateSafe(ctx context.Context, id uuid.UUID, req dtos.UpdateSafeReq) error {
	var safe entities.Safe
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&safe).Error
	if err != nil {
		return err
	}

	safe.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&safe).Error
}

func (r *repository) DeleteSafe(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&entities.Safe{}).Where("id = ?", id).Update("deleted_at", "NOW()").Error
}

func (r *repository) AddMoney(ctx context.Context, id uuid.UUID, amount float32) error {
	var safe entities.Safe
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&safe).Error
	if err != nil {
		return err
	}

	safe.AddMoney(amount)
	return r.db.WithContext(ctx).Save(&safe).Error
}

func (r *repository) WithdrawMoney(ctx context.Context, id uuid.UUID, amount float32) error {
	var safe entities.Safe
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&safe).Error
	if err != nil {
		return err
	}

	err = safe.WithdrawMoney(amount)
	if err != nil {
		return err
	}

	return r.db.WithContext(ctx).Save(&safe).Error
}

func (r *repository) GetTotalAmount(ctx context.Context) (float32, error) {
	var total float32

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := ctx.Value("organization_id")
	if organizationID == nil {
		return 0, errors.New("organization ID not found in context")
	}

	whereClause := "deleted_at IS NULL AND organization_id = ?"
	args := []any{organizationID}

	err := r.db.WithContext(ctx).Model(&entities.Safe{}).Where(whereClause, args...).Select("COALESCE(SUM(amount), 0)").Scan(&total).Error
	return total, err
}

func (r *repository) GetFirstSafe(ctx context.Context) (*entities.Safe, error) {
	var safe entities.Safe

	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return nil, errors.New("sub-organization ID not found in context")
	}

	whereClause := "deleted_at IS NULL AND sub_organization_id = ?"
	args := []any{subOrganizationID}

	err := r.db.WithContext(ctx).Where(whereClause, args...).First(&safe).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &safe, nil
}

func (r *repository) HasAnySafe(ctx context.Context) (bool, error) {
	var count int64

	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return false, errors.New("sub-organization ID not found in context")
	}

	whereClause := "deleted_at IS NULL AND sub_organization_id = ?"
	args := []any{subOrganizationID}

	err := r.db.WithContext(ctx).Model(&entities.Safe{}).Where(whereClause, args...).Count(&count).Error
	return count > 0, err
}
