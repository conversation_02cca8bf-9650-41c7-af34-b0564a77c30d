package productstock

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateProductStock(ctx context.Context, req dtos.CreateProductStockReq) error
	GetProductStockByProductAndSubOrg(ctx context.Context, productID, subOrgID uuid.UUID) (*entities.ProductStock, error)
	UpdateProductStock(ctx context.Context, id uuid.UUID, req dtos.UpdateProductStockReq) error
	AddStock(ctx context.Context, req dtos.AddStockReq) error
	ReduceStock(ctx context.Context, productID, subOrgID uuid.UUID, quantity int) error
	GetStockByProduct(ctx context.Context, productID uuid.UUID) ([]entities.ProductStock, error)
	GetTotalStockByProduct(ctx context.Context, productID uuid.UUID) (int, error)
	GetAllProductStocks(ctx context.Context) ([]entities.ProductStock, error)
	GetProductStocksBySubOrg(ctx context.Context, subOrgID uuid.UUID) ([]entities.ProductStock, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateProductStock(ctx context.Context, req dtos.CreateProductStockReq) error {
	var productStock entities.ProductStock
	productStock.Mapper(req)
	return r.db.WithContext(ctx).Create(&productStock).Error
}

func (r *repository) GetProductStockByProductAndSubOrg(ctx context.Context, productID, subOrgID uuid.UUID) (*entities.ProductStock, error) {
	var productStock entities.ProductStock
	err := r.db.WithContext(ctx).Where("product_id = ? AND sub_organization_id = ? AND deleted_at IS NULL", productID, subOrgID).First(&productStock).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &productStock, nil
}

func (r *repository) UpdateProductStock(ctx context.Context, id uuid.UUID, req dtos.UpdateProductStockReq) error {
	var productStock entities.ProductStock
	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&productStock).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("product stock not found")
		}
		return err
	}

	productStock.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&productStock).Error
}

func (r *repository) AddStock(ctx context.Context, req dtos.AddStockReq) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Check if stock record exists
		var productStock entities.ProductStock
		err := tx.Where("product_id = ? AND sub_organization_id = ? AND deleted_at IS NULL", req.ProductID, req.SubOrganizationID).First(&productStock).Error
		
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Create new stock record
				productStock = entities.ProductStock{
					ProductID:         req.ProductID,
					SubOrganizationID: req.SubOrganizationID,
					Quantity:          req.Quantity,
				}
				return tx.Create(&productStock).Error
			}
			return err
		}

		// Update existing stock
		productStock.AddStock(req.Quantity)
		return tx.Save(&productStock).Error
	})
}

func (r *repository) ReduceStock(ctx context.Context, productID, subOrgID uuid.UUID, quantity int) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var productStock entities.ProductStock
		err := tx.Where("product_id = ? AND sub_organization_id = ? AND deleted_at IS NULL", productID, subOrgID).First(&productStock).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return errors.New("product stock not found")
			}
			return err
		}

		if err := productStock.ReduceStock(quantity); err != nil {
			return err
		}

		return tx.Save(&productStock).Error
	})
}

func (r *repository) GetStockByProduct(ctx context.Context, productID uuid.UUID) ([]entities.ProductStock, error) {
	var stocks []entities.ProductStock
	
	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := ctx.Value("organization_id")
	if organizationID == nil {
		return nil, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).
		Joins("JOIN sub_organizations ON product_stocks.sub_organization_id = sub_organizations.id").
		Where("product_stocks.product_id = ? AND product_stocks.deleted_at IS NULL AND sub_organizations.organization_id = ?", productID, organizationID).
		Find(&stocks).Error
	
	return stocks, err
}

func (r *repository) GetTotalStockByProduct(ctx context.Context, productID uuid.UUID) (int, error) {
	var totalStock int64
	
	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := ctx.Value("organization_id")
	if organizationID == nil {
		return 0, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).
		Table("product_stocks").
		Joins("JOIN sub_organizations ON product_stocks.sub_organization_id = sub_organizations.id").
		Where("product_stocks.product_id = ? AND product_stocks.deleted_at IS NULL AND sub_organizations.organization_id = ?", productID, organizationID).
		Select("COALESCE(SUM(product_stocks.quantity), 0)").
		Scan(&totalStock).Error
	
	return int(totalStock), err
}

func (r *repository) GetAllProductStocks(ctx context.Context) ([]entities.ProductStock, error) {
	var stocks []entities.ProductStock
	
	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return nil, errors.New("sub-organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("sub_organization_id = ? AND deleted_at IS NULL", subOrganizationID).Find(&stocks).Error
	return stocks, err
}

func (r *repository) GetProductStocksBySubOrg(ctx context.Context, subOrgID uuid.UUID) ([]entities.ProductStock, error) {
	var stocks []entities.ProductStock
	err := r.db.WithContext(ctx).Where("sub_organization_id = ? AND deleted_at IS NULL", subOrgID).Find(&stocks).Error
	return stocks, err
}
