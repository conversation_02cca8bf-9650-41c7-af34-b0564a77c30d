package productstock

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/state"
	"context"
	"errors"

	"github.com/google/uuid"
)

type Service interface {
	CreateProductStock(ctx context.Context, req dtos.CreateProductStockReq) error
	GetProductStockByProductAndSubOrg(ctx context.Context, productID, subOrgID uuid.UUID) (*dtos.ProductStockResponse, error)
	UpdateProductStock(ctx context.Context, id uuid.UUID, req dtos.UpdateProductStockReq) error
	AddStock(ctx context.Context, req dtos.AddStockReq) error
	ReduceStock(ctx context.Context, productID, subOrgID uuid.UUID, quantity int) error
	GetStockByProduct(ctx context.Context, productID uuid.UUID) ([]dtos.ProductStockResponse, error)
	GetTotalStockByProduct(ctx context.Context, productID uuid.UUID) (int, error)
	GetAllProductStocks(ctx context.Context) ([]dtos.ProductStockResponse, error)
	GetProductStocksBySubOrg(ctx context.Context, subOrgID uuid.UUID) ([]dtos.ProductStockResponse, error)
	GetProductStockSummary(ctx context.Context, productID uuid.UUID) (*dtos.ProductStockSummaryResponse, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateProductStock(ctx context.Context, req dtos.CreateProductStockReq) error {
	// Set sub-organization ID from context if not provided
	if req.SubOrganizationID == uuid.Nil {
		subOrgID := state.CurrentUserSubOrganization(ctx)
		if subOrgID == uuid.Nil {
			return errors.New("sub-organization ID not found in context")
		}
		req.SubOrganizationID = subOrgID
	}

	return s.repo.CreateProductStock(ctx, req)
}

func (s *service) GetProductStockByProductAndSubOrg(ctx context.Context, productID, subOrgID uuid.UUID) (*dtos.ProductStockResponse, error) {
	productStock, err := s.repo.GetProductStockByProductAndSubOrg(ctx, productID, subOrgID)
	if err != nil {
		return nil, err
	}

	if productStock == nil {
		return nil, nil
	}

	response := productStock.ToResponse()
	return &response, nil
}

func (s *service) UpdateProductStock(ctx context.Context, id uuid.UUID, req dtos.UpdateProductStockReq) error {
	return s.repo.UpdateProductStock(ctx, id, req)
}

func (s *service) AddStock(ctx context.Context, req dtos.AddStockReq) error {
	// Set sub-organization ID from context if not provided
	if req.SubOrganizationID == uuid.Nil {
		subOrgID := state.CurrentUserSubOrganization(ctx)
		if subOrgID == uuid.Nil {
			return errors.New("sub-organization ID not found in context")
		}
		req.SubOrganizationID = subOrgID
	}

	return s.repo.AddStock(ctx, req)
}

func (s *service) ReduceStock(ctx context.Context, productID, subOrgID uuid.UUID, quantity int) error {
	return s.repo.ReduceStock(ctx, productID, subOrgID, quantity)
}

func (s *service) GetStockByProduct(ctx context.Context, productID uuid.UUID) ([]dtos.ProductStockResponse, error) {
	stocks, err := s.repo.GetStockByProduct(ctx, productID)
	if err != nil {
		return nil, err
	}

	var responses []dtos.ProductStockResponse
	for _, stock := range stocks {
		responses = append(responses, stock.ToResponse())
	}

	return responses, nil
}

func (s *service) GetTotalStockByProduct(ctx context.Context, productID uuid.UUID) (int, error) {
	return s.repo.GetTotalStockByProduct(ctx, productID)
}

func (s *service) GetAllProductStocks(ctx context.Context) ([]dtos.ProductStockResponse, error) {
	stocks, err := s.repo.GetAllProductStocks(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.ProductStockResponse
	for _, stock := range stocks {
		responses = append(responses, stock.ToResponse())
	}

	return responses, nil
}

func (s *service) GetProductStocksBySubOrg(ctx context.Context, subOrgID uuid.UUID) ([]dtos.ProductStockResponse, error) {
	stocks, err := s.repo.GetProductStocksBySubOrg(ctx, subOrgID)
	if err != nil {
		return nil, err
	}

	var responses []dtos.ProductStockResponse
	for _, stock := range stocks {
		responses = append(responses, stock.ToResponse())
	}

	return responses, nil
}

func (s *service) GetProductStockSummary(ctx context.Context, productID uuid.UUID) (*dtos.ProductStockSummaryResponse, error) {
	// Get all stock details for the product
	stockDetails, err := s.GetStockByProduct(ctx, productID)
	if err != nil {
		return nil, err
	}

	// Calculate total quantity
	totalQuantity := 0
	for _, stock := range stockDetails {
		totalQuantity += stock.Quantity
	}

	// Get product info (you might need to inject product repository here)
	// For now, we'll return basic info
	response := &dtos.ProductStockSummaryResponse{
		ProductID:     productID,
		TotalQuantity: totalQuantity,
		StockDetails:  stockDetails,
	}

	return response, nil
}
