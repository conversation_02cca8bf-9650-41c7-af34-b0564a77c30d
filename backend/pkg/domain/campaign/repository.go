package campaign

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateCampaign(ctx context.Context, req dtos.CreateCampaignReq) error
	GetAllCampaigns(ctx context.Context) ([]entities.Campaign, error)
	GetCampaignsPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Campaign, int64, error)
	GetCampaignByID(ctx context.Context, id uuid.UUID) (*entities.Campaign, error)
	UpdateCampaign(ctx context.Context, id uuid.UUID, req dtos.UpdateCampaignReq) error
	DeleteCampaign(ctx context.Context, id uuid.UUID) error
	GetActiveCampaigns(ctx context.Context) ([]entities.Campaign, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateCampaign(ctx context.Context, req dtos.CreateCampaignReq) error {
	var campaign entities.Campaign
	campaign.Mapper(req)
	return r.db.WithContext(ctx).Create(&campaign).Error
}

func (r *repository) GetAllCampaigns(ctx context.Context) ([]entities.Campaign, error) {
	var campaigns []entities.Campaign

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := ctx.Value("organization_id")
	fmt.Printf("DEBUG: Repository GetAllCampaigns - organization_id from context: %v\n", organizationID)
	if organizationID == nil {
		return nil, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND organization_id = ?", organizationID).Order("created_at DESC").Find(&campaigns).Error
	return campaigns, err
}

func (r *repository) GetCampaignsPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Campaign, int64, error) {
	var campaigns []entities.Campaign
	var total int64

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := ctx.Value("organization_id")
	if organizationID == nil {
		return nil, 0, errors.New("organization ID not found in context")
	}

	whereClause := "deleted_at IS NULL AND organization_id = ?"
	args := []any{organizationID}

	// Count total records
	err := r.db.WithContext(ctx).Model(&entities.Campaign{}).Where(whereClause, args...).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	offset := pagination.CalculateOffset()
	err = r.db.WithContext(ctx).
		Where(whereClause, args...).
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&campaigns).Error

	return campaigns, total, err
}

func (r *repository) GetCampaignByID(ctx context.Context, id uuid.UUID) (*entities.Campaign, error) {
	var campaign entities.Campaign

	// ZORUNLU: Organizasyon ID filtresi - state paketinden al
	organizationID := ctx.Value("organization_id")
	if organizationID == nil {
		return nil, errors.New("organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL AND organization_id = ?", id, organizationID).First(&campaign).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &campaign, nil
}

func (r *repository) UpdateCampaign(ctx context.Context, id uuid.UUID, req dtos.UpdateCampaignReq) error {
	var campaign entities.Campaign
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&campaign).Error
	if err != nil {
		return err
	}

	campaign.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&campaign).Error
}

func (r *repository) DeleteCampaign(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&entities.Campaign{}).Where("id = ?", id).Update("deleted_at", "NOW()").Error
}

func (r *repository) GetActiveCampaigns(ctx context.Context) ([]entities.Campaign, error) {
	var campaigns []entities.Campaign
	now := time.Now().Format("2006-01-02")
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND start_date <= ? AND end_date >= ?", now, now).Order("created_at DESC").Find(&campaigns).Error
	return campaigns, err
}
