package campaign

import (
	"business-mamagement/pkg/dtos"
	"context"

	"github.com/google/uuid"
)

type Service interface {
	CreateCampaign(ctx context.Context, req dtos.CreateCampaignReq) error
	GetAllCampaigns(ctx context.Context) ([]dtos.CampaignResponse, error)
	GetCampaignsPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.CampaignResponse], error)
	GetCampaignByID(ctx context.Context, id uuid.UUID) (*dtos.CampaignResponse, error)
	UpdateCampaign(ctx context.Context, id uuid.UUID, req dtos.UpdateCampaignReq) error
	DeleteCampaign(ctx context.Context, id uuid.UUID) error
	GetActiveCampaigns(ctx context.Context) ([]dtos.CampaignResponse, error)
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateCampaign(ctx context.Context, req dtos.CreateCampaignReq) error {
	return s.repo.CreateCampaign(ctx, req)
}

func (s *service) GetAllCampaigns(ctx context.Context) ([]dtos.CampaignResponse, error) {
	campaigns, err := s.repo.GetAllCampaigns(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CampaignResponse
	for _, campaign := range campaigns {
		responses = append(responses, campaign.ToResponse())
	}
	return responses, nil
}

func (s *service) GetCampaignsPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.CampaignResponse], error) {
	campaigns, total, err := s.repo.GetCampaignsPaginated(ctx, pagination)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CampaignResponse
	for _, campaign := range campaigns {
		responses = append(responses, campaign.ToResponse())
	}

	paginationResponse := dtos.NewPaginationResponse(pagination.Page, pagination.PerPage, total)

	return &dtos.PaginatedResponse[dtos.CampaignResponse]{
		Data:       responses,
		Pagination: paginationResponse,
	}, nil
}

func (s *service) GetCampaignByID(ctx context.Context, id uuid.UUID) (*dtos.CampaignResponse, error) {
	campaign, err := s.repo.GetCampaignByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if campaign == nil {
		return nil, nil
	}

	response := campaign.ToResponse()
	return &response, nil
}

func (s *service) UpdateCampaign(ctx context.Context, id uuid.UUID, req dtos.UpdateCampaignReq) error {
	return s.repo.UpdateCampaign(ctx, id, req)
}

func (s *service) DeleteCampaign(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteCampaign(ctx, id)
}

func (s *service) GetActiveCampaigns(ctx context.Context) ([]dtos.CampaignResponse, error) {
	campaigns, err := s.repo.GetActiveCampaigns(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CampaignResponse
	for _, campaign := range campaigns {
		responses = append(responses, campaign.ToResponse())
	}
	return responses, nil
}
