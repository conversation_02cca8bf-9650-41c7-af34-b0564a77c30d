package organization

import (
	"business-mamagement/pkg/dtos"
	"context"

	"github.com/google/uuid"
)

type Service interface {
	CreateOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error
	CreateSubOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error
	GetAllOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error)
	GetMainOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error)
	GetSubOrganizations(ctx context.Context, mainOrgID uuid.UUID) ([]dtos.OrganizationResponse, error)
	GetOrganizationsPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.OrganizationResponse], error)
	GetOrganizationByID(ctx context.Context, id uuid.UUID) (*dtos.OrganizationResponse, error)
	UpdateOrganization(ctx context.Context, id uuid.UUID, req dtos.UpdateOrganizationReq) error
	DeleteOrganization(ctx context.Context, id uuid.UUID) error
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error {
	// Main organization oluştururken IsMain=true, MainOrgID=nil
	req.IsMain = true
	req.MainOrgID = nil
	return s.repo.CreateOrganization(ctx, req)
}

func (s *service) CreateSubOrganization(ctx context.Context, req dtos.CreateOrganizationReq) error {
	req.IsMain = false
	return s.repo.CreateOrganization(ctx, req)
}

func (s *service) GetAllOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error) {
	organizations, err := s.repo.GetAllOrganizations(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, org.ToResponse())
	}

	return responses, nil
}

func (s *service) GetMainOrganizations(ctx context.Context) ([]dtos.OrganizationResponse, error) {
	organizations, err := s.repo.GetMainOrganizations(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, org.ToResponse())
	}

	return responses, nil
}

func (s *service) GetSubOrganizations(ctx context.Context, mainOrgID uuid.UUID) ([]dtos.OrganizationResponse, error) {
	organizations, err := s.repo.GetSubOrganizations(ctx, mainOrgID)
	if err != nil {
		return nil, err
	}

	var responses []dtos.OrganizationResponse
	for _, org := range organizations {
		responses = append(responses, org.ToResponse())
	}

	return responses, nil
}

func (s *service) GetOrganizationsPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.OrganizationResponse], error) {
	return s.repo.GetOrganizationsPaginated(ctx, req)
}

func (s *service) GetOrganizationByID(ctx context.Context, id uuid.UUID) (*dtos.OrganizationResponse, error) {
	organization, err := s.repo.GetOrganizationByID(ctx, id)
	if err != nil {
		return nil, err
	}

	response := organization.ToResponse()
	return &response, nil
}

func (s *service) UpdateOrganization(ctx context.Context, id uuid.UUID, req dtos.UpdateOrganizationReq) error {
	return s.repo.UpdateOrganization(ctx, id, req)
}

func (s *service) DeleteOrganization(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteOrganization(ctx, id)
}
