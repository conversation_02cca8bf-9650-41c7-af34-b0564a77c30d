package category

import (
	"business-mamagement/pkg/dtos"
	"context"

	"github.com/google/uuid"
)

type Service interface {
	CreateCategory(ctx context.Context, req dtos.CreateCategoryReq) error
	GetAllCategories(ctx context.Context) ([]dtos.CategoryResponse, error)
	GetCategoriesPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.CategoryResponse], error)
	GetCategoryByID(ctx context.Context, id uuid.UUID) (*dtos.CategoryResponse, error)
	UpdateCategory(ctx context.Context, id uuid.UUID, req dtos.UpdateCategoryReq) error
	DeleteCategory(ctx context.Context, id uuid.UUID) error
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateCategory(ctx context.Context, req dtos.CreateCategoryReq) error {
	return s.repo.CreateCategory(ctx, req)
}

func (s *service) GetAllCategories(ctx context.Context) ([]dtos.CategoryResponse, error) {
	categories, err := s.repo.GetAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CategoryResponse
	for _, category := range categories {
		responses = append(responses, category.ToResponse())
	}
	return responses, nil
}

func (s *service) GetCategoriesPaginated(ctx context.Context, pagination dtos.PaginationRequest) (*dtos.PaginatedResponse[dtos.CategoryResponse], error) {
	categories, total, err := s.repo.GetCategoriesPaginated(ctx, pagination)
	if err != nil {
		return nil, err
	}

	var responses []dtos.CategoryResponse
	for _, category := range categories {
		responses = append(responses, category.ToResponse())
	}

	paginationResponse := dtos.NewPaginationResponse(pagination.Page, pagination.PerPage, total)

	return &dtos.PaginatedResponse[dtos.CategoryResponse]{
		Data:       responses,
		Pagination: paginationResponse,
	}, nil
}

func (s *service) GetCategoryByID(ctx context.Context, id uuid.UUID) (*dtos.CategoryResponse, error) {
	category, err := s.repo.GetCategoryByID(ctx, id)
	if err != nil {
		return nil, err
	}

	if category == nil {
		return nil, nil
	}

	response := category.ToResponse()
	return &response, nil
}

func (s *service) UpdateCategory(ctx context.Context, id uuid.UUID, req dtos.UpdateCategoryReq) error {
	return s.repo.UpdateCategory(ctx, id, req)
}

func (s *service) DeleteCategory(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteCategory(ctx, id)
}
