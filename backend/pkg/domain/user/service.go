package user

import (
	"business-mamagement/pkg/config"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type Service interface {
	CreateUser(ctx context.Context, req dtos.CreateUserReq) error
	GetAllUsers(ctx context.Context) ([]dtos.UserResponse, error)
	GetUsersPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.UserResponse], error)
	GetUserByID(ctx context.Context, id uuid.UUID) (*dtos.UserResponse, error)
	UpdateUser(ctx context.Context, id uuid.UUID, req dtos.UpdateUserReq) error
	DeleteUser(ctx context.Context, id uuid.UUID) error
	Login(ctx context.Context, req dtos.LoginReq) (*dtos.LoginResponse, error)
}

type service struct {
	repo Repository
}

type Claims struct {
	UserID            string `json:"user_id"`
	Username          string `json:"username"`
	Role              string `json:"role"`
	OrganizationID    string `json:"organization_id"`
	SubOrganizationID string `json:"sub_organization_id"`

	// State package fields
	AdminID      string `json:"admin_id,omitempty"`
	AdminToken   string `json:"admin_token,omitempty"`
	DepartmentID string `json:"department_id,omitempty"`
	UserIP       string `json:"user_ip,omitempty"`
	AdminAgent   string `json:"admin_agent,omitempty"`
	IsMainOrg    bool   `json:"is_main_org,omitempty"`
	IsAuthorized bool   `json:"is_authorized,omitempty"`
	ApiKey       string `json:"api_key,omitempty"`
	ApiSecret    string `json:"api_secret,omitempty"`
	InternalAuth bool   `json:"internal_auth,omitempty"`

	jwt.RegisteredClaims
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) CreateUser(ctx context.Context, req dtos.CreateUserReq) error {
	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	req.Password = string(hashedPassword)

	return s.repo.CreateUser(ctx, req)
}

func (s *service) GetAllUsers(ctx context.Context) ([]dtos.UserResponse, error) {
	users, err := s.repo.GetAllUsers(ctx)
	if err != nil {
		return nil, err
	}

	var responses []dtos.UserResponse
	for _, user := range users {
		responses = append(responses, user.ToResponse())
	}

	return responses, nil
}

func (s *service) GetUsersPaginated(ctx context.Context, req dtos.PaginationRequest) (dtos.PaginatedResponse[dtos.UserResponse], error) {
	return s.repo.GetUsersPaginated(ctx, req)
}

func (s *service) GetUserByID(ctx context.Context, id uuid.UUID) (*dtos.UserResponse, error) {
	user, err := s.repo.GetUserByID(ctx, id)
	if err != nil {
		return nil, err
	}

	response := user.ToResponse()
	return &response, nil
}

func (s *service) UpdateUser(ctx context.Context, id uuid.UUID, req dtos.UpdateUserReq) error {
	// Hash password if provided
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		req.Password = string(hashedPassword)
	}

	return s.repo.UpdateUser(ctx, id, req)
}

func (s *service) DeleteUser(ctx context.Context, id uuid.UUID) error {
	return s.repo.DeleteUser(ctx, id)
}

func (s *service) Login(ctx context.Context, req dtos.LoginReq) (*dtos.LoginResponse, error) {
	// Get user by username
	user, err := s.repo.GetUserByUsername(ctx, req.Username)
	if err != nil {
		// Debug log
		println("DEBUG: User not found for username:", req.Username, "Error:", err.Error())
		return nil, errors.New("invalid username or password")
	}

	// Check if user is active
	if !user.IsActive {
		return nil, errors.New("user account is disabled")
	}

	// Check password
	println("DEBUG: Comparing password for user:", user.Username)
	println("DEBUG: Stored hash:", user.Password)
	println("DEBUG: Input password:", req.Password)
	err = bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password))
	if err != nil {
		println("DEBUG: Password comparison failed:", err.Error())
		return nil, errors.New("invalid username or password")
	}

	// Generate JWT token
	token, err := s.generateToken(user, ctx)
	if err != nil {
		return nil, err
	}

	return &dtos.LoginResponse{
		Token: token,
		User:  user.ToResponse(),
	}, nil
}

func (s *service) generateToken(user *entities.User, ctx context.Context) (string, error) {
	cfg := config.ReadValue()

	// Get additional context information
	userIP := ""
	if ip := ctx.Value("CurrentIP"); ip != nil {
		userIP = ip.(string)
	}

	adminAgent := ""
	if agent := ctx.Value("User-Agent"); agent != nil {
		adminAgent = agent.(string)
	}

	claims := Claims{
		UserID:            user.ID.String(),
		Username:          user.Username,
		Role:              user.Role,
		OrganizationID:    user.OrganizationID.String(),
		SubOrganizationID: user.SubOrganizationID.String(),

		// State package fields - set from context or defaults
		AdminID:      user.ID.String(), // Admin ID same as user ID for now
		DepartmentID: "",               // Can be set based on user's department if exists
		UserIP:       userIP,           // Get from context
		AdminAgent:   adminAgent,       // Get from context
		IsMainOrg:    true,             // Default to true, can be configured
		IsAuthorized: true,             // Default to true for authenticated users
		InternalAuth: false,            // Default to false for regular login

		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(cfg.JwtExpire) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    cfg.JwtIssuer,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JwtSecret))
}
