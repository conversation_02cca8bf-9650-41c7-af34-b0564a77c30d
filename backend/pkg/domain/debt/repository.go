package debt

import (
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/entities"
	"context"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Repository interface {
	CreateDebt(ctx context.Context, req dtos.CreateDebtReq) error
	GetAllDebts(ctx context.Context) ([]entities.Debt, error)
	GetDebtsPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Debt, int64, error)
	GetDebtByID(ctx context.Context, id uuid.UUID) (*entities.Debt, error)
	UpdateDebt(ctx context.Context, id uuid.UUID, req dtos.UpdateDebtReq) error
	DeleteDebt(ctx context.Context, id uuid.UUID) error
	PayDebt(ctx context.Context, id uuid.UUID, amount float32) error
	GetUnpaidDebts(ctx context.Context) ([]entities.Debt, error)
	GetPaidDebts(ctx context.Context) ([]entities.Debt, error)
	GetFirstSafe(ctx context.Context) (*entities.Safe, error)
	AddMoneyToSafe(ctx context.Context, safeID uuid.UUID, amount float32) error
	GetDebtsByDateRange(ctx context.Context, startDate, endDate string) ([]entities.Debt, error)
	GetDebtsByPaymentStatus(ctx context.Context, isPaid bool) ([]entities.Debt, error)
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) CreateDebt(ctx context.Context, req dtos.CreateDebtReq) error {
	var debt entities.Debt
	debt.Mapper(req)
	return r.db.WithContext(ctx).Create(&debt).Error
}

func (r *repository) GetAllDebts(ctx context.Context) ([]entities.Debt, error) {
	var debts []entities.Debt

	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return nil, errors.New("sub-organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND sub_organization_id = ?", subOrganizationID).Order("created_at DESC").Find(&debts).Error
	return debts, err
}

func (r *repository) GetDebtsPaginated(ctx context.Context, pagination dtos.PaginationRequest) ([]entities.Debt, int64, error) {
	var debts []entities.Debt
	var total int64

	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return nil, 0, errors.New("sub-organization ID not found in context")
	}

	whereClause := "deleted_at IS NULL AND sub_organization_id = ?"
	args := []any{subOrganizationID}

	// Count total records
	err := r.db.WithContext(ctx).Model(&entities.Debt{}).Where(whereClause, args...).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Get paginated records
	offset := pagination.CalculateOffset()
	err = r.db.WithContext(ctx).
		Where(whereClause, args...).
		Order("created_at DESC").
		Limit(pagination.PerPage).
		Offset(offset).
		Find(&debts).Error

	return debts, total, err
}

func (r *repository) GetDebtByID(ctx context.Context, id uuid.UUID) (*entities.Debt, error) {
	var debt entities.Debt

	// ZORUNLU: Sub-organizasyon ID filtresi - state paketinden al
	subOrganizationID := ctx.Value("sub_organization_id")
	if subOrganizationID == nil {
		return nil, errors.New("sub-organization ID not found in context")
	}

	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL AND sub_organization_id = ?", id, subOrganizationID).First(&debt).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &debt, nil
}

func (r *repository) UpdateDebt(ctx context.Context, id uuid.UUID, req dtos.UpdateDebtReq) error {
	var debt entities.Debt
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&debt).Error
	if err != nil {
		return err
	}

	debt.UpdateMapper(req)
	return r.db.WithContext(ctx).Save(&debt).Error
}

func (r *repository) DeleteDebt(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(&entities.Debt{}).Where("id = ?", id).Update("deleted_at", "NOW()").Error
}

func (r *repository) PayDebt(ctx context.Context, id uuid.UUID, amount float32) error {
	var debt entities.Debt
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&debt).Error
	if err != nil {
		return err
	}

	debt.PayDebt(amount)
	return r.db.WithContext(ctx).Save(&debt).Error
}

func (r *repository) GetUnpaidDebts(ctx context.Context) ([]entities.Debt, error) {
	var debts []entities.Debt
	err := r.db.WithContext(ctx).Where("is_paid = ? AND deleted_at IS NULL", false).Find(&debts).Error
	return debts, err
}

func (r *repository) GetPaidDebts(ctx context.Context) ([]entities.Debt, error) {
	var debts []entities.Debt
	err := r.db.WithContext(ctx).Where("is_paid = ? AND deleted_at IS NULL", true).Find(&debts).Error
	return debts, err
}

func (r *repository) GetFirstSafe(ctx context.Context) (*entities.Safe, error) {
	var safe entities.Safe
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL").First(&safe).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &safe, nil
}

func (r *repository) AddMoneyToSafe(ctx context.Context, safeID uuid.UUID, amount float32) error {
	var safe entities.Safe
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", safeID).First(&safe).Error
	if err != nil {
		return err
	}

	safe.AddMoney(amount)
	return r.db.WithContext(ctx).Save(&safe).Error
}

func (r *repository) GetDebtsByDateRange(ctx context.Context, startDate, endDate string) ([]entities.Debt, error) {
	var debts []entities.Debt
	query := r.db.WithContext(ctx).Where("deleted_at IS NULL").Order("created_at DESC")

	if startDate != "" && endDate != "" {
		query = query.Where("DATE(created_at) BETWEEN ? AND ?", startDate, endDate)
	} else if startDate != "" {
		query = query.Where("DATE(created_at) >= ?", startDate)
	} else if endDate != "" {
		query = query.Where("DATE(created_at) <= ?", endDate)
	}

	err := query.Find(&debts).Error
	return debts, err
}

func (r *repository) GetDebtsByPaymentStatus(ctx context.Context, isPaid bool) ([]entities.Debt, error) {
	var debts []entities.Debt
	err := r.db.WithContext(ctx).Where("deleted_at IS NULL AND is_paid = ?", isPaid).Order("created_at DESC").Find(&debts).Error
	return debts, err
}
