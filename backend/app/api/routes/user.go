package routes

import (
	"business-mamagement/pkg/domain/user"
	"business-mamagement/pkg/dtos"
	"business-mamagement/pkg/infrastructure/middleware"
	"context"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Context key types to avoid collisions
type contextKey string

const (
	currentIPKey contextKey = "CurrentIP"
	userAgentKey contextKey = "User-Agent"
)

func UserRoutes(r *gin.RouterGroup, s user.Service) {
	// Public routes
	r.POST("/auth/login", login(s))
	r.GET("/auth/test", testUsers(s))
	r.POST("/auth/fix-admin", fixAdminRole(s))
	r.POST("/auth/reset-admin-password", resetAdminPassword(s))

	// Protected routes (require authentication)
	auth := r.Group("/")
	auth.Use(middleware.AuthMiddleware())
	{
		// Admin only routes
		admin := auth.Group("/")
		admin.Use(middleware.AdminMiddleware())
		{
			admin.POST("/users", createUser(s))
			admin.GET("/users", getAllUsers(s))
			admin.GET("/users/paginated", getUsersPaginated(s))
			admin.GET("/users/:id", getUserByID(s))
			admin.PUT("/users/:id", updateUser(s))
			admin.DELETE("/users/:id", deleteUser(s))
		}
	}
}

func login(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.LoginReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Add IP and User-Agent to context for token generation
		ctx := c.Request.Context()
		ctx = context.WithValue(ctx, currentIPKey, c.ClientIP())
		ctx = context.WithValue(ctx, userAgentKey, c.GetHeader("User-Agent"))
		c.Request = c.Request.WithContext(ctx)

		response, err := s.Login(c, req)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": response})
	}
}

func createUser(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateUserReq
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.CreateUser(c, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "User created successfully"})
	}
}

func getAllUsers(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		users, err := s.GetAllUsers(c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": users})
	}
}

func getUsersPaginated(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
		perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "10"))

		req := dtos.PaginationRequest{
			Page:    page,
			PerPage: perPage,
		}

		response, err := s.GetUsersPaginated(c, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, response)
	}
}

func getUserByID(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			return
		}

		user, err := s.GetUserByID(c, id)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": user})
	}
}

func updateUser(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			return
		}

		var req dtos.UpdateUserReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err = s.UpdateUser(c, id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
	}
}

func deleteUser(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := uuid.Parse(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			return
		}

		err = s.DeleteUser(c, id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
	}
}

func testUsers(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		users, err := s.GetAllUsers(c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"users": users, "count": len(users)})
	}
}

func fixAdminRole(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Find admin user and update role
		users, err := s.GetAllUsers(c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		for _, user := range users {
			if user.Username == "admin" {
				updateReq := dtos.UpdateUserReq{
					Role: "admin",
				}
				err := s.UpdateUser(c, user.ID, updateReq)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusOK, gin.H{"message": "Admin role fixed successfully"})
				return
			}
		}

		c.JSON(http.StatusNotFound, gin.H{"error": "Admin user not found"})
	}
}

func resetAdminPassword(s user.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		// Find admin user and reset password to 123456
		users, err := s.GetAllUsers(c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		for _, user := range users {
			if user.Username == "admin" {
				updateReq := dtos.UpdateUserReq{
					Password: "123456",
				}
				err := s.UpdateUser(c, user.ID, updateReq)
				if err != nil {
					c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
					return
				}
				c.JSON(http.StatusOK, gin.H{"message": "Admin password reset to 123456"})
				return
			}
		}

		c.JSON(http.StatusNotFound, gin.H{"error": "Admin user not found"})
	}
}
