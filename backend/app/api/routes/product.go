package routes

import (
	"business-mamagement/pkg/domain/product"
	"business-mamagement/pkg/dtos"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func ProductRoutes(r *gin.RouterGroup, s product.Service) {
	r.POST("/products", createProduct(s))
	r.GET("/products", getAllProducts(s))
	r.GET("/products/paginated", getProductsPaginated(s))
	r.GET("/products/:id", getProductByID(s))
	r.PUT("/products/:id", updateProduct(s))
	r.DELETE("/products/:id", deleteProduct(s))
	r.GET("/products/search", searchProducts(s))
	r.POST("/products/import-excel", importProductsFromExcel(s))
}

func createProduct(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.CreateProductReq
		err := c.Should<PERSON>ind<PERSON>odyWithJSON(&req)
		if err != nil {
			c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Get organization_id from middleware
		organizationID, exists := c.Get("organization_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Organization ID not found"})
			return
		}

		// Parse organization_id to UUID
		orgUUID, err := uuid.Parse(organizationID.(string))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
			return
		}

		// Set organization_id in request
		req.OrganizationID = orgUUID

		err = s.CreateProduct(c, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, gin.H{"message": "Product created successfully"})
	}
}

func getAllProducts(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		products, err := s.GetAllProducts(c)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": products})
	}
}

func getProductsPaginated(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var pagination dtos.PaginationRequest

		// Parse query parameters
		page := 1
		perPage := 10

		if pageStr := c.Query("page"); pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
				page = p
			}
		}

		if perPageStr := c.Query("per_page"); perPageStr != "" {
			if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
				perPage = pp
			}
		}

		pagination = dtos.NewPaginationRequest(page, perPage)

		result, err := s.GetProductsPaginated(c, pagination)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	}
}

func getProductByID(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
			return
		}

		product, err := s.GetProductByID(c, id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": product})
	}
}

func updateProduct(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
			return
		}

		var req dtos.UpdateProductReq
		err = c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Get organization_id from middleware
		organizationID, exists := c.Get("organization_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Organization ID not found"})
			return
		}

		// Parse organization_id to UUID
		orgUUID, err := uuid.Parse(organizationID.(string))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid organization ID"})
			return
		}

		// Set organization_id in request
		req.OrganizationID = orgUUID

		err = s.UpdateProduct(c, id, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Product updated successfully"})
	}
}

func deleteProduct(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		idParam := c.Param("id")
		id, err := uuid.Parse(idParam)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid product ID"})
			return
		}

		err = s.DeleteProduct(c, id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "Product deleted successfully"})
	}
}

func searchProducts(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		query := c.Query("q")
		if query == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Search query is required"})
			return
		}

		products, err := s.SearchProducts(c, query)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": products})
	}
}

func importProductsFromExcel(s product.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.ExcelImportRequest
		err := c.ShouldBindBodyWithJSON(&req)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		result, err := s.ImportProductsFromExcel(c, req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": result})
	}
}
