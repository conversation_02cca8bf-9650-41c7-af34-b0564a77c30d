'use client';

import { useState, useEffect, useRef } from 'react';
import { Product } from '@/types';
import { productService } from '@/services/productService';

interface ProductSearchProps {
  onProductSelect: (product: Product) => void;
  placeholder?: string;
  disabled?: boolean;
}

export default function ProductSearch({
  onProductSelect,
  placeholder = "Ürün adı veya kodu arayın...",
  disabled = false
}: ProductSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Debounced search effect
  useEffect(() => {
    const searchProducts = async () => {
      if (searchTerm.trim() === '' || searchTerm.trim().length < 2) {
        setFilteredProducts([]);
        setIsOpen(false);
        return;
      }

      try {
        setLoading(true);
        // Backend search API'sini kullan - hem ürün adı hem de ürün koduna göre arama yapar
        const results = await productService.search(searchTerm);
        // Tüm ürünleri göster (stok kontrolü satış sırasında yapılacak)
        setFilteredProducts(results);
        setIsOpen(results.length > 0);
      } catch (error) {
        console.error('Error searching products:', error);
        setFilteredProducts([]);
        setIsOpen(false);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchProducts, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchTerm]);

  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  const handleProductClick = (product: Product) => {
    onProductSelect(product);
    setSearchTerm('');
    setFilteredProducts([]);
    setIsOpen(false);
  };

  return (
    <div ref={searchRef} className="relative">
      <div className="relative">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
          </div>
        )}
      </div>

      {isOpen && filteredProducts.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              onClick={() => handleProductClick(product)}
              className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
            >
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-medium text-gray-900">{product.name}</div>
                  <div className="text-sm text-gray-500">Kod: {product.product_code}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {product.discounted_price < product.price ? (
                      <>
                        <span className="line-through text-gray-400">{product.price.toFixed(2)} TL</span>
                        <span className="ml-1 text-green-600">{product.discounted_price.toFixed(2)} TL</span>
                      </>
                    ) : (
                      <span>{product.price.toFixed(2)} TL</span>
                    )}
                  </div>
                  <div className={`text-xs ${product.quantity > 0 ? 'text-green-600' : 'text-red-500'}`}>
                    Stok: {product.quantity} {product.quantity === 0 && '(Tükendi)'}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {isOpen && filteredProducts.length === 0 && searchTerm.trim() !== '' && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="px-3 py-2 text-gray-500 text-center">
            Ürün bulunamadı
          </div>
        </div>
      )}
    </div>
  );
}
