'use client';

import { useState, useEffect, useRef } from 'react';
import { CustomerSearchResult } from '@/types';
import { customerService } from '@/services/customerService';
import { Users } from 'lucide-react';

interface CustomerSuggestionProps {
  value: string;
  onChange: (value: string) => void;
  onCustomerSelect: (customer: CustomerSearchResult) => void;
  placeholder?: string;
  label?: string;
  required?: boolean;
}

export default function CustomerSuggestion({
  value,
  onChange,
  onCustomerSelect,
  placeholder = "TC Kimlik No",
  label = "TC Kimlik No",
  required = false
}: CustomerSuggestionProps) {
  const [suggestions, setSuggestions] = useState<CustomerSearchResult[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [loading, setLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const searchCustomers = async () => {
      if (value.length >= 2) {
        setLoading(true);
        try {
          const results = await customerService.searchByTC(value);
          setSuggestions(results);
          setShowSuggestions(results.length > 0);
        } catch (error) {
          console.error('Error searching customers:', error);
          setSuggestions([]);
          setShowSuggestions(false);
        } finally {
          setLoading(false);
        }
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    };

    const debounceTimer = setTimeout(searchCustomers, 300);
    return () => clearTimeout(debounceTimer);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  const handleSuggestionClick = (customer: CustomerSearchResult) => {
    onChange(customer.tc);
    onCustomerSelect(customer);
    setShowSuggestions(false);
  };

  return (
    <div className="relative w-full">
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          placeholder={placeholder}
          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          onFocus={() => {
            if (suggestions.length > 0) {
              setShowSuggestions(true);
            }
          }}
        />
        
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto"
        >
          {suggestions.map((customer) => (
            <div
              key={customer.id}
              onClick={() => handleSuggestionClick(customer)}
              className="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
            >
              <div className="flex items-center">
                <Users className="h-4 w-4 text-gray-400 mr-3" />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">
                    {customer.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    TC: {customer.tc}
                    {customer.phone && ` • Tel: ${customer.phone}`}
                    {customer.address && ` • Adres: ${customer.address}`}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
