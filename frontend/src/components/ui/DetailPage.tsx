'use client';

import { ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader } from './Card';
import Button from './Button';
import { ArrowLeft, Eye } from 'lucide-react';

interface DetailPageProps {
  title: string;
  subtitle?: string;
  loading?: boolean;
  error?: string | null;
  onBack?: () => void;
  backUrl?: string;
  children: ReactNode;
  actions?: ReactNode;
}

export default function DetailPage({
  title,
  subtitle,
  loading = false,
  error = null,
  onBack,
  backUrl,
  children,
  actions
}: DetailPageProps) {
  const router = useRouter();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else if (backUrl) {
      router.push(backUrl);
    } else {
      router.back();
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              onClick={handleBack}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Geri
            </Button>
          </div>
          
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-red-600 text-lg font-medium mb-2">Hata</div>
              <div className="text-gray-600">{error}</div>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              onClick={handleBack}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Geri
            </Button>
            
            <div>
              <div className="flex items-center">
                <Eye className="h-5 w-5 text-green-600 mr-2" />
                <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
              </div>
              {subtitle && (
                <p className="text-gray-600 mt-1">{subtitle}</p>
              )}
            </div>
          </div>
          
          {actions && (
            <div className="flex items-center space-x-4">
              {actions}
            </div>
          )}
        </div>

        {/* Content */}
        {children}
      </div>
    </MainLayout>
  );
}

interface DetailSectionProps {
  title: string;
  children: ReactNode;
  className?: string;
}

export function DetailSection({ title, children, className = '' }: DetailSectionProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <h2 className="text-lg font-semibold text-gray-900">{title}</h2>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}

interface DetailFieldProps {
  label: string;
  value: string | number | ReactNode;
  className?: string;
}

export function DetailField({ label, value, className = '' }: DetailFieldProps) {
  return (
    <div className={`flex justify-between py-2 border-b border-gray-100 last:border-b-0 ${className}`}>
      <span className="text-gray-600 font-medium">{label}:</span>
      <span className="text-gray-900">{value}</span>
    </div>
  );
}

interface DetailGridProps {
  children: ReactNode;
  columns?: 1 | 2 | 3;
  className?: string;
}

export function DetailGrid({ children, columns = 2, className = '' }: DetailGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
  };

  return (
    <div className={`grid ${gridCols[columns]} gap-6 ${className}`}>
      {children}
    </div>
  );
}
