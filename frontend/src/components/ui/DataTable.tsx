'use client';

import { ReactNode } from 'react';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from './Table';
import Pagination, { PaginationInfo } from './Pagination';

export interface Column<T> {
  key: string;
  header: string;
  render?: (item: T) => ReactNode;
  className?: string;
}

interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
  onPerPageChange: (perPage: number) => void;
  loading?: boolean;
  emptyMessage?: string;
  emptyIcon?: ReactNode;
  className?: string;
  useClientPagination?: boolean; // Frontend pagination için
}

export default function DataTable<T extends { id?: string | number }>({
  columns,
  data,
  pagination,
  onPageChange,
  onPerPageChange,
  loading = false,
  emptyMessage = 'Veri bulunamadı',
  emptyIcon,
  className,
  useClientPagination = false
}: DataTableProps<T>) {
  // Client-side pagination logic
  let displayData = data;
  let paginationInfo = pagination;

  if (useClientPagination) {
    const totalItems = data.length;
    const totalPages = Math.ceil(totalItems / pagination.per_page);
    const startIndex = (pagination.page - 1) * pagination.per_page;
    const endIndex = startIndex + pagination.per_page;

    displayData = data.slice(startIndex, endIndex);
    paginationInfo = {
      page: pagination.page,
      per_page: pagination.per_page,
      total: totalItems,
      total_pages: totalPages,
      has_next: pagination.page < totalPages,
      has_prev: pagination.page > 1
    };
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <Table className={className}>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.key} className={column.className}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: paginationInfo.per_page }).map((_, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell key={column.key} className={column.className}>
                    <div className="animate-pulse bg-gray-200 h-4 rounded"></div>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <div className="flex justify-center py-4">
          <div className="animate-pulse bg-gray-200 h-8 w-64 rounded"></div>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        {emptyIcon && <div className="flex justify-center mb-4">{emptyIcon}</div>}
        <h3 className="text-lg font-medium text-gray-900 mb-2">{emptyMessage}</h3>
        <p className="text-gray-600">Henüz veri bulunmuyor.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Table className={className}>
        <TableHeader>
          <TableRow>
            {columns.map((column) => (
              <TableHead key={column.key} className={column.className}>
                {column.header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {displayData.map((item, index) => (
            <TableRow key={item.id || index}>
              {columns.map((column) => (
                <TableCell key={column.key} className={column.className}>
                  {column.render ? column.render(item) : String((item as Record<string, unknown>)[column.key] || '')}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
      
      <Pagination
        pagination={paginationInfo}
        onPageChange={onPageChange}
        onPerPageChange={onPerPageChange}
      />
    </div>
  );
}
