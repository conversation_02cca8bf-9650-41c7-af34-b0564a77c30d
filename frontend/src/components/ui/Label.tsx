import clsx from 'clsx';
import { LabelHTMLAttributes } from 'react';

interface LabelProps extends LabelHTMLAttributes<HTMLLabelElement> {
  className?: string;
  children: React.ReactNode;
}

export function Label({ className, children, ...props }: LabelProps) {
  return (
    <label
      className={clsx(
        'text-sm font-medium text-gray-700 block mb-1',
        className
      )}
      {...props}
    >
      {children}
    </label>
  );
}

export default Label;
