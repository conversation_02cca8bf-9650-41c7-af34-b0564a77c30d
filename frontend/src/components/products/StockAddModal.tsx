'use client';

import { useState, useEffect } from 'react';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Search, Package, Plus } from 'lucide-react';
import { productService } from '@/services/productService';
import { Product } from '@/types';

interface StockAddModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function StockAddModal({ isOpen, onClose, onSuccess }: StockAddModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [addQuantity, setAddQuantity] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  // Search products when query changes
  useEffect(() => {
    const searchProducts = async () => {
      if (searchQuery.trim().length < 2) {
        setSearchResults([]);
        return;
      }

      setIsSearching(true);
      try {
        const results = await productService.search(searchQuery);
        setSearchResults(results);
      } catch (error) {
        console.error('Error searching products:', error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    const debounceTimer = setTimeout(searchProducts, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setSearchQuery(product.name);
    setSearchResults([]);
  };

  const handleAddStock = async () => {
    if (!selectedProduct || addQuantity <= 0) {
      alert('Lütfen ürün seçin ve geçerli bir miktar girin.');
      return;
    }

    setIsLoading(true);
    try {
      const newQuantity = selectedProduct.quantity + addQuantity;
      await productService.update(selectedProduct.id, { quantity: newQuantity });
      
      handleClose();
      onSuccess();
    } catch (error) {
      console.error('Error adding stock:', error);
      alert('Stok eklenirken hata oluştu: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSelectedProduct(null);
    setAddQuantity(0);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title="Stok Ekle" size="lg">
      <div className="space-y-4 min-h-[400px]">
        {/* Product Search */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ürün Ara
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Ürün adı veya kodu ile ara..."
              className="pl-10"
            />
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-xl max-h-80 overflow-y-auto">
              {searchResults.map((product) => (
                <button
                  key={product.id}
                  onClick={() => handleProductSelect(product)}
                  className="w-full px-4 py-4 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 min-h-[80px]"
                >
                  <div className="flex items-start">
                    <Package className="h-4 w-4 text-blue-600 mr-3 mt-1 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 leading-5 mb-1 break-words">
                        {product.name}
                      </div>
                      <div className="text-sm text-gray-500 leading-4">
                        Kod: {product.product_code} | Mevcut Stok: {product.quantity}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}

          {isSearching && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-4 text-center">
              <div className="text-gray-500">Aranıyor...</div>
            </div>
          )}
        </div>

        {/* Selected Product Info */}
        {selectedProduct && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Seçilen Ürün</h4>
            <div className="space-y-1 text-sm text-blue-800">
              <p><strong>Ürün:</strong> {selectedProduct.name}</p>
              <p><strong>Kod:</strong> {selectedProduct.product_code}</p>
              <p><strong>Mevcut Stok:</strong> {selectedProduct.quantity} adet</p>
            </div>
          </div>
        )}

        {/* Add Quantity */}
        {selectedProduct && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Eklenecek Miktar
            </label>
            <Input
              type="number"
              value={addQuantity}
              onChange={(e) => setAddQuantity(parseInt(e.target.value) || 0)}
              placeholder="Eklenecek adet sayısı"
              min="1"
            />
            {addQuantity > 0 && (
              <p className="text-sm text-gray-600 mt-1">
                Yeni stok miktarı: <strong>{selectedProduct.quantity + addQuantity} adet</strong>
              </p>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="secondary" onClick={handleClose}>
            İptal
          </Button>
          <Button 
            onClick={handleAddStock}
            disabled={!selectedProduct || addQuantity <= 0 || isLoading}
          >
            <Plus className="h-4 w-4 mr-2" />
            {isLoading ? 'Ekleniyor...' : 'Stok Ekle'}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
