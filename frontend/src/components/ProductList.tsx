'use client';

import { SaleItem, Product } from '@/types';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';

interface ProductListProps {
  items: SaleItem[];
  products: Product[];
  onUpdateItem: (index: number, updates: Partial<SaleItem>) => void;
  onRemoveItem: (index: number) => void;
  totalAmount: number;
}

export default function ProductList({
  items,
  products,
  onUpdateItem,
  onRemoveItem,
  totalAmount
}: ProductListProps) {
  const handleQuantityChange = (index: number, quantity: number) => {
    const item = items[index];
    const product = products.find(p => p.id === item.product_id);

    if (!product) {
      console.error('Product not found for item:', item.product_id);
      return;
    }

    // Stok kontrolü - girilen miktar stok miktarından fazla olamaz
    if (quantity > product.quantity) {
      alert(`Bu ürün için maksimum ${product.quantity} adet satış yapabilirsiniz. Mevcut stok: ${product.quantity}`);
      return;
    }

    // Minimum 1 adet kontrolü
    if (quantity < 1) {
      quantity = 1;
    }

    const newTotalPrice = item.unit_price * quantity;
    onUpdateItem(index, {
      quantity,
      total_price: newTotalPrice
    });
  };

  const handleUnitPriceChange = (index: number, unitPrice: number) => {
    const item = items[index];
    const newTotalPrice = unitPrice * item.quantity;
    onUpdateItem(index, {
      unit_price: unitPrice,
      total_price: newTotalPrice
    });
  };

  const handleTotalPriceChange = (index: number, totalPrice: number) => {
    const item = items[index];
    const newUnitPrice = item.quantity > 0 ? totalPrice / item.quantity : 0;
    onUpdateItem(index, {
      unit_price: newUnitPrice,
      total_price: totalPrice
    });
  };

  if (items.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        Henüz ürün eklenmedi. Yukarıdaki arama kutusundan ürün arayarak ekleyebilirsiniz.
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Seçilen Ürünler</h3>
      
      <div className="space-y-3">
        {items.map((item, index) => {
          const product = products.find(p => p.id === item.product_id);
          const maxQuantity = product?.quantity || 0;

          return (
            <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{item.product_name}</h4>
                    <span className="text-sm text-gray-500">Stok: {maxQuantity}</span>
                  </div>
                
                <div className="grid grid-cols-4 gap-3 mt-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Miktar
                    </label>
                    <Input
                      type="number"
                      min="1"
                      max={maxQuantity}
                      value={item.quantity}
                      onChange={(e) => handleQuantityChange(index, parseInt(e.target.value) || 1)}
                      className="text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Birim Fiyat
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.unit_price}
                      onChange={(e) => handleUnitPriceChange(index, parseFloat(e.target.value) || 0)}
                      className="text-sm"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Satıcı İndirimi
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.seller_discount_amount || 0}
                      onChange={(e) => onUpdateItem(index, { seller_discount_amount: parseFloat(e.target.value) || 0 })}
                      className="text-sm"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Toplam
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      min="0"
                      value={item.total_price.toFixed(2)}
                      onChange={(e) => handleTotalPriceChange(index, parseFloat(e.target.value) || 0)}
                      className="text-sm"
                    />
                  </div>
                </div>

                {/* Cashback Information */}
                {(item.campaign_discount_amount || item.original_price) && (
                  <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <h5 className="text-sm font-medium text-blue-900 mb-2">Kampanya Bilgileri</h5>
                    <div className="grid grid-cols-3 gap-3 text-sm">
                      <div>
                        <span className="text-blue-700">Orijinal Fiyat:</span>
                        <span className="ml-1 font-medium">{(item.original_price || 0).toFixed(2)} TL</span>
                      </div>
                      <div>
                        <span className="text-blue-700">Kampanya İndirimi:</span>
                        <span className="ml-1 font-medium">{(item.campaign_discount_amount || 0).toFixed(2)} TL</span>
                      </div>
                      <div>
                        <span className="text-blue-700">Kasaya Gidecek:</span>
                        <span className="ml-1 font-medium text-green-600">
                          {(item.total_price + (item.campaign_discount_amount || 0)).toFixed(2)} TL
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <Button
                variant="secondary"
                size="sm"
                onClick={() => onRemoveItem(index)}
                className="ml-3 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                Kaldır
              </Button>
            </div>
          </div>
          );
        })}
      </div>
      
      <div className="border-t border-gray-200 pt-4">
        <div className="flex justify-between items-center">
          <span className="text-lg font-medium text-gray-900">Genel Toplam:</span>
          <span className="text-xl font-bold text-gray-900">{totalAmount.toFixed(2)} TL</span>
        </div>
      </div>
    </div>
  );
}
