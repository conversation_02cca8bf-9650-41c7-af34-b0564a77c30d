'use client';

import { useAppState } from '@/hooks/useAppState';
import { useState } from 'react';

export default function StateDebugger() {
  const [isOpen, setIsOpen] = useState(false);
  const appState = useAppState();

  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-gray-800 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-700"
      >
        {isOpen ? 'Hide' : 'Show'} State
      </button>
      
      {isOpen && (
        <div className="absolute bottom-12 right-0 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-96 max-h-96 overflow-y-auto">
          <h3 className="text-lg font-semibold mb-3 text-gray-900">App State Debug</h3>
          
          <div className="space-y-2 text-sm">
            <div className="border-b pb-2">
              <h4 className="font-medium text-gray-700">User State</h4>
              <div className="text-gray-600">
                <div>User ID: {appState.currentUserID || 'null'}</div>
                <div>Organization ID: {appState.currentUserOrganizationID || 'null'}</div>
              </div>
            </div>
            
            <div className="border-b pb-2">
              <h4 className="font-medium text-gray-700">Admin State</h4>
              <div className="text-gray-600">
                <div>Admin ID: {appState.currentAdminID || 'null'}</div>
                <div>Admin Token: {appState.currentAdminToken ? '***' : 'null'}</div>
                <div>Department ID: {appState.currentDepartmentID || 'null'}</div>
                <div>Is Main Org: {appState.isMainOrg ? 'true' : 'false'}</div>
                <div>Is Authorized: {appState.isAuthorized ? 'true' : 'false'}</div>
              </div>
            </div>
            
            <div className="border-b pb-2">
              <h4 className="font-medium text-gray-700">Network State</h4>
              <div className="text-gray-600">
                <div>User IP: {appState.currentUserIP || 'null'}</div>
                <div>Admin Agent: {appState.currentAdminAgent || 'null'}</div>
              </div>
            </div>
            
            <div className="border-b pb-2">
              <h4 className="font-medium text-gray-700">API State</h4>
              <div className="text-gray-600">
                <div>API Key: {appState.apiKey ? '***' : 'null'}</div>
                <div>API Secret: {appState.apiSecret ? '***' : 'null'}</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-700">Auth State</h4>
              <div className="text-gray-600">
                <div>Internal Auth: {appState.internalAuth ? 'true' : 'false'}</div>
                <div>Is Admin: {appState.isAdmin ? 'true' : 'false'}</div>
                <div>Is Authenticated: {appState.isAuthenticated ? 'true' : 'false'}</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
