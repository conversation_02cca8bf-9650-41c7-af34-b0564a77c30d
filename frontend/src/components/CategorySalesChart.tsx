'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { BarChart3, Package, TrendingUp } from 'lucide-react';
import { CategorySales } from '@/types';
import { reportService } from '@/services/reportService';

interface CategorySalesChartProps {
  year: number;
  month: number;
}

export default function CategorySalesChart({ year, month }: CategorySalesChartProps) {
  const [categories, setCategories] = useState<CategorySales[]>([]);
  const [loading, setLoading] = useState(true);

  const loadCategorySales = useCallback(async () => {
    try {
      setLoading(true);
      const data = await reportService.getCategorySales(
        year || undefined,
        month || undefined
      );
      setCategories(data);
    } catch (error) {
      console.error('Error loading category sales:', error);
    } finally {
      setLoading(false);
    }
  }, [year, month]);

  useEffect(() => {
    loadCategorySales();
  }, [loadCategorySales]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <div className="flex items-center">
            <BarChart3 className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Kategori Bazlı Satışlar</h3>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="text-gray-500">Yükleniyor...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const maxRevenue = Math.max(...categories.map(c => c.total_revenue), 1);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BarChart3 className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Kategori Bazlı Satışlar</h3>
          </div>
          <div className="text-sm text-gray-500">
            {month ? `${month}/${year}` : year}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {categories.length > 0 ? (
          <div className="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
            <div className="space-y-4 pr-2">
              {categories.map((category, index) => {
                const percentage = (category.total_revenue / maxRevenue) * 100;

                return (
                  <div key={category.category_id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                          <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900">{category.category_name}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm">
                        <div className="flex items-center text-gray-600">
                          <Package className="h-3 w-3 mr-1" />
                          {category.total_sales} adet
                        </div>
                        <div className="flex items-center text-green-600 font-medium">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          {category.total_revenue.toFixed(2)} TL
                        </div>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>

                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{category.product_count} ürün</span>
                      <span>%{((category.total_revenue / categories.reduce((sum, c) => sum + c.total_revenue, 0)) * 100).toFixed(1)}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Bu dönemde kategori satış verisi bulunamadı</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
