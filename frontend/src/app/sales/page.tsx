'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, ShoppingCart, Search, Eye } from 'lucide-react';
import { saleService } from '@/services/saleService';
import { productService } from '@/services/productService';
import { Sale, CreateSaleRequest, CreateMultiSaleRequest, UpdateSaleRequest, Product, SaleItem, CustomerSearchResult, PaginationRequest } from '@/types';
import ProductSearch from '@/components/ProductSearch';
import ProductList from '@/components/ProductList';
import CustomerSuggestion from '@/components/CustomerSuggestion';
import { useAuth } from '@/contexts/AuthContext';

export default function SalesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingSale, setEditingSale] = useState<Sale | null>(null);
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });

  // Search states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');

  // Multi-product sale form data
  const [multiSaleData, setMultiSaleData] = useState<CreateMultiSaleRequest>({
    items: [],
    customer_name: '',
    customer_phone: '',
    customer_tc: '',
    customer_address: '',
    sale_date: new Date().toISOString().split('T')[0],
    is_paid: true,
    total_amount: 0,
  });

  // Legacy single product form data (for edit operations)
  const [formData, setFormData] = useState<CreateSaleRequest>({
    product_id: '',
    quantity: 1,
    unit_price: 0,
    total_price: 0,
    customer_name: '',
    customer_phone: '',
    customer_tc: '',
    customer_address: '',
    sale_date: new Date().toISOString().split('T')[0],
    is_paid: true,
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [salesData, productsData] = await Promise.all([
        saleService.getAll(),
        productService.getAll(),
      ]);
      setSales(salesData);
      setProducts(productsData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Search functions
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setSearchInput('');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleProductChange = (productId: string) => {
    const selectedProduct = products.find(p => p.id === productId);
    if (selectedProduct) {
      // Use discounted price if available, otherwise use regular price
      const effectivePrice = selectedProduct.discounted_price < selectedProduct.price
        ? selectedProduct.discounted_price
        : selectedProduct.price;

      setFormData(prev => ({
        ...prev,
        product_id: productId,
        unit_price: effectivePrice,
        total_price: effectivePrice * prev.quantity,
      }));
    }
  };

  const handleQuantityChange = (quantity: number) => {
    setFormData(prev => ({
      ...prev,
      quantity,
      total_price: prev.unit_price * quantity,
    }));
  };



  // Multi-product sale functions
  const handleProductSelect = (product: Product) => {
    // Stok kontrolü
    if (product.quantity <= 0) {
      alert(`"${product.name}" ürünü stokta bulunmuyor. Mevcut stok: ${product.quantity}`);
      return;
    }

    // Aynı ürün daha önce eklenmiş mi kontrol et
    const existingItemIndex = multiSaleData.items.findIndex(item => item.product_id === product.id);
    if (existingItemIndex !== -1) {
      const existingItem = multiSaleData.items[existingItemIndex];
      const newQuantity = existingItem.quantity + 1;

      // Toplam miktar stok miktarını aşmasın
      if (newQuantity > product.quantity) {
        alert(`Bu ürün için maksimum ${product.quantity} adet satış yapabilirsiniz. Şu anda ${existingItem.quantity} adet ekli.`);
        return;
      }

      // Mevcut ürünün miktarını artır
      handleUpdateItem(existingItemIndex, {
        quantity: newQuantity,
        total_price: existingItem.unit_price * newQuantity
      });
      return;
    }

    const effectivePrice = product.discounted_price < product.price
      ? product.discounted_price
      : product.price;

    const newItem: SaleItem = {
      product_id: product.id,
      product_name: product.name,
      quantity: 1,
      unit_price: effectivePrice,
      total_price: effectivePrice,
    };

    setMultiSaleData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
      total_amount: prev.total_amount + effectivePrice,
    }));
  };

  const handleUpdateItem = (index: number, updates: Partial<SaleItem>) => {
    setMultiSaleData(prev => {
      const newItems = [...prev.items];
      newItems[index] = { ...newItems[index], ...updates };

      const newTotalAmount = newItems.reduce((sum, item) => sum + item.total_price, 0);

      return {
        ...prev,
        items: newItems,
        total_amount: newTotalAmount,
      };
    });
  };

  const handleRemoveItem = (index: number) => {
    setMultiSaleData(prev => {
      const newItems = prev.items.filter((_, i) => i !== index);
      const newTotalAmount = newItems.reduce((sum, item) => sum + item.total_price, 0);

      return {
        ...prev,
        items: newItems,
        total_amount: newTotalAmount,
      };
    });
  };

  const handleCustomerSelect = (customer: CustomerSearchResult) => {
    setMultiSaleData(prev => ({
      ...prev,
      customer_name: customer.name,
      customer_phone: customer.phone,
      customer_tc: customer.tc,
      customer_address: customer.address,
    }));
  };

  const handleEditCustomerSelect = (customer: CustomerSearchResult) => {
    setFormData(prev => ({
      ...prev,
      customer_name: customer.name,
      customer_phone: customer.phone,
      customer_tc: customer.tc,
      customer_address: customer.address,
    }));
  };

  const handleCreateMultiSale = async () => {
    if (multiSaleData.items.length === 0) {
      alert('En az bir ürün eklemelisiniz.');
      return;
    }

    try {
      // Format date to include time in the correct format
      const saleDateTime = multiSaleData.sale_date + ' ' + new Date().toTimeString().split(' ')[0];
      const dataToSend = {
        ...multiSaleData,
        sale_date: saleDateTime,
        organization_id: user?.organization_id || ''
      };

      await saleService.createMulti(dataToSend);
      setIsCreateModalOpen(false);
      setMultiSaleData({
        items: [],
        customer_name: '',
        customer_phone: '',
        customer_tc: '',
        customer_address: '',
        sale_date: new Date().toISOString().split('T')[0],
        is_paid: true,
        total_amount: 0,
      });
      loadData();
    } catch (error) {
      console.error('Error creating multi sale:', error);
      alert('Satış oluşturulurken hata oluştu: ' + (error as Error).message);
    }
  };

  const openEditModal = (sale: Sale) => {
    setEditingSale(sale);
    setFormData({
      product_id: sale.product_id,
      quantity: sale.quantity,
      unit_price: sale.unit_price,
      total_price: sale.total_price,
      customer_name: sale.customer_name,
      customer_phone: sale.customer_phone,
      customer_tc: sale.customer_tc,
      customer_address: sale.customer_address,
      sale_date: sale.sale_date.split(' ')[0], // Extract date part
      is_paid: sale.is_paid,
    });
    setIsEditModalOpen(true);
  };

  const handleEdit = async () => {
    if (!editingSale) return;
    
    try {
      // Format date to include time if not already present
      let saleDate = formData.sale_date;
      if (!saleDate.includes(' ')) {
        saleDate = formData.sale_date + ' ' + new Date().toTimeString().split(' ')[0];
      }

      const updateData: UpdateSaleRequest = {
        quantity: formData.quantity,
        unit_price: formData.unit_price,
        total_price: formData.total_price,
        customer_name: formData.customer_name,
        customer_phone: formData.customer_phone,
        customer_tc: formData.customer_tc,
        sale_date: saleDate,
      };
      
      await saleService.update(editingSale.id, updateData);
      setIsEditModalOpen(false);
      setEditingSale(null);
      loadData();
    } catch (error) {
      console.error('Error updating sale:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu satışı silmek istediğinizden emin misiniz?')) return;
    
    try {
      await saleService.delete(id);
      loadData();
    } catch (error) {
      console.error('Error deleting sale:', error);
    }
  };



  const formatDate = (dateString: string) => {
    // If the date is already in the correct format (YYYY-MM-DD HH:mm:ss), just format it nicely
    if (dateString.includes(' ')) {
      const [datePart, timePart] = dateString.split(' ');
      const [year, month, day] = datePart.split('-');
      const [hour, minute] = timePart.split(':');
      return `${day}.${month}.${year} ${hour}:${minute}`;
    }
    // If it's just a date (YYYY-MM-DD), format it
    const [year, month, day] = dateString.split('-');
    return `${day}.${month}.${year}`;
  };

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  // Normalize Turkish characters for search
  const normalizeTurkish = (text: string) => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c');
  };

  // Filter sales based on search term
  const filteredSales = sales.filter(sale => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeTurkish(searchTerm);
    return (
      normalizeTurkish(sale.customer_name).includes(normalizedSearchTerm) ||
      normalizeTurkish(sale.customer_phone).includes(normalizedSearchTerm) ||
      normalizeTurkish(sale.customer_tc).includes(normalizedSearchTerm) ||
      normalizeTurkish(sale.product_name).includes(normalizedSearchTerm)
    );
  });

  const columns: Column<Sale>[] = [
    {
      key: 'customer',
      header: 'Müşteri',
      render: (sale) => (
        <div className="flex items-center">
          <ShoppingCart className="h-5 w-5 text-green-600 mr-2" />
          <div>
            <div className="font-medium text-gray-900">{sale.customer_name}</div>
            <div className="text-sm text-gray-500">{sale.customer_phone}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'product',
      header: 'Ürün',
      render: (sale) => (
        <div>
          <div className="font-medium">{sale.product_name}</div>
          <div className="text-sm text-gray-500">Adet: {sale.quantity}</div>
        </div>
      ),
    },
    {
      key: 'amount',
      header: 'Tutar',
      render: (sale) => (
        <div className="font-medium">{formatPrice(sale.total_price)}</div>
      ),
    },
    {
      key: 'payment',
      header: 'Ödeme',
      render: (sale) => (
        <span className={`px-2 py-1 rounded-full text-xs ${
          sale.is_paid
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {sale.is_paid ? 'Ödendi' : 'Ödenmedi'}
        </span>
      ),
    },
    {
      key: 'date',
      header: 'Tarih',
      render: (sale) => (
        <div className="text-sm">{formatDate(sale.sale_date)}</div>
      ),
    },
    {
      key: 'actions',
      header: 'İşlemler',
      className: 'text-right',
      render: (sale) => (
        <div className="flex justify-end space-x-2">
          <Button
            size="sm"
            variant="success"
            onClick={() => router.push(`/sales/${sale.id}`)}
            title="Detay"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="info"
            onClick={() => openEditModal(sale)}
            title="Düzenle"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(sale.id)}
            title="Sil"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <ShoppingCart className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Satışlar</h1>
              <p className="text-gray-600">Satış işlemlerini yönetin</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Satış
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Müşteri adı, telefon, TC veya ürün ara..."
                value={searchInput}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <Button
              onClick={handleSearch}
              variant="primary"
              className="px-4 py-2"
            >
              Ara
            </Button>
            {(searchTerm) && (
              <Button
                onClick={handleClearSearch}
                variant="secondary"
                className="px-4 py-2"
              >
                Temizle
              </Button>
            )}
          </div>

          {/* Search Summary */}
          {searchTerm && (
            <div className="mt-4 text-sm text-gray-600">
              {filteredSales.length} satış gösteriliyor • &quot;{searchTerm}&quot; araması
            </div>
          )}
        </div>

        {/* Sales Display */}
        <DataTable
          columns={columns}
          data={filteredSales}
          pagination={{ page: pagination.page, per_page: pagination.per_page, total: filteredSales.length, total_pages: Math.ceil(filteredSales.length / pagination.per_page), has_next: pagination.page < Math.ceil(filteredSales.length / pagination.per_page), has_prev: pagination.page > 1 }}
          onPageChange={handlePageChange}
          onPerPageChange={handlePerPageChange}
          loading={loading}
          emptyMessage="Henüz satış kaydı bulunmuyor"
          emptyIcon={<ShoppingCart className="h-12 w-12 text-gray-400" />}
          useClientPagination={true}
        />
      </div>

      {/* Create Sale Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          setMultiSaleData({
            items: [],
            customer_name: '',
            customer_phone: '',
            customer_tc: '',
            customer_address: '',
            sale_date: new Date().toISOString().split('T')[0],
            is_paid: true,
            total_amount: 0,
          });
        }}
        title="Yeni Satış"
        size="lg"
      >
        <div className="space-y-6">
          {/* Product Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Ürün Arama
            </label>
            <ProductSearch onProductSelect={handleProductSelect} />
          </div>

          {/* Selected Products List */}
          <ProductList
            items={multiSaleData.items}
            products={products}
            onUpdateItem={handleUpdateItem}
            onRemoveItem={handleRemoveItem}
            totalAmount={multiSaleData.total_amount}
          />

          {/* Customer Information */}
          <div className="border-t border-gray-200 pt-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Müşteri Bilgileri</h3>

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Müşteri Adı"
                value={multiSaleData.customer_name}
                onChange={(e) => setMultiSaleData(prev => ({ ...prev, customer_name: e.target.value }))}
              />

              <Input
                label="Müşteri Telefonu"
                value={multiSaleData.customer_phone}
                onChange={(e) => setMultiSaleData(prev => ({ ...prev, customer_phone: e.target.value }))}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <CustomerSuggestion
                label="Müşteri TC"
                value={multiSaleData.customer_tc || ''}
                onChange={(value) => setMultiSaleData(prev => ({ ...prev, customer_tc: value }))}
                onCustomerSelect={handleCustomerSelect}
              />

              <Input
                label="Satış Tarihi"
                type="date"
                value={multiSaleData.sale_date}
                onChange={(e) => setMultiSaleData(prev => ({ ...prev, sale_date: e.target.value }))}
              />
            </div>

            <div className="mt-4">
              <Input
                label="Müşteri Adresi"
                value={multiSaleData.customer_address}
                onChange={(e) => setMultiSaleData(prev => ({ ...prev, customer_address: e.target.value }))}
                placeholder="Müşteri adresini girin..."
              />
            </div>

            <div className="space-y-2 mt-4">
              <label className="block text-sm font-medium text-gray-700">
                Ödeme Durumu
              </label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="payment_status"
                    checked={multiSaleData.is_paid}
                    onChange={() => setMultiSaleData(prev => ({ ...prev, is_paid: true }))}
                    className="mr-2"
                  />
                  Ödendi
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="payment_status"
                    checked={!multiSaleData.is_paid}
                    onChange={() => setMultiSaleData(prev => ({ ...prev, is_paid: false }))}
                    className="mr-2"
                  />
                  Ödenmedi (Borç)
                </label>
              </div>
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => {
                setIsCreateModalOpen(false);
                setMultiSaleData({
                  items: [],
                  customer_name: '',
                  customer_phone: '',
                  customer_tc: '',
                  customer_address: '',
                  sale_date: new Date().toISOString().split('T')[0],
                  is_paid: true,
                  total_amount: 0,
                });
              }}
              className="flex-1"
            >
              İptal
            </Button>
            <Button
              onClick={handleCreateMultiSale}
              className="flex-1"
              disabled={multiSaleData.items.length === 0}
            >
              Satış Oluştur
            </Button>
          </div>
        </div>
      </Modal>

      {/* Edit Sale Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Satışı Düzenle"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Ürün
            </label>
            <select
              value={formData.product_id}
              onChange={(e) => handleProductChange(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm text-gray-900 bg-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Ürün seçin</option>
              {products.map((product) => (
                <option key={product.id} value={product.id}>
                  {product.name} (Stok: {product.quantity})
                </option>
              ))}
            </select>
          </div>

          <Input
            label="Miktar"
            type="number"
            min="1"
            value={formData.quantity}
            onChange={(e) => handleQuantityChange(parseInt(e.target.value) || 1)}
          />

          <Input
            label="Birim Fiyat"
            type="number"
            step="0.01"
            value={formData.unit_price}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              unit_price: parseFloat(e.target.value) || 0,
              total_price: (parseFloat(e.target.value) || 0) * prev.quantity,
            }))}
          />

          <Input
            label="Toplam Fiyat"
            type="number"
            step="0.01"
            value={formData.total_price}
            readOnly
          />

          <Input
            label="Müşteri Adı"
            value={formData.customer_name}
            onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
          />

          <Input
            label="Müşteri Telefonu"
            value={formData.customer_phone}
            onChange={(e) => setFormData(prev => ({ ...prev, customer_phone: e.target.value }))}
          />

          <CustomerSuggestion
            label="Müşteri TC"
            value={formData.customer_tc || ''}
            onChange={(value) => setFormData(prev => ({ ...prev, customer_tc: value }))}
            onCustomerSelect={handleEditCustomerSelect}
          />

          <Input
            label="Müşteri Adresi"
            value={formData.customer_address}
            onChange={(e) => setFormData(prev => ({ ...prev, customer_address: e.target.value }))}
            placeholder="Müşteri adresini girin..."
          />

          <Input
            label="Satış Tarihi"
            type="date"
            value={formData.sale_date}
            onChange={(e) => setFormData(prev => ({ ...prev, sale_date: e.target.value }))}
          />

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Ödeme Durumu
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="payment_status_edit"
                  checked={formData.is_paid}
                  onChange={() => setFormData(prev => ({ ...prev, is_paid: true }))}
                  className="mr-2"
                />
                Ödendi
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="payment_status_edit"
                  checked={!formData.is_paid}
                  onChange={() => setFormData(prev => ({ ...prev, is_paid: false }))}
                  className="mr-2"
                />
                Ödenmedi (Borç)
              </label>
            </div>
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="secondary"
              onClick={() => setIsEditModalOpen(false)}
              className="flex-1"
            >
              İptal
            </Button>
            <Button
              onClick={handleEdit}
              className="flex-1"
              disabled={!formData.product_id || formData.quantity < 1}
            >
              Güncelle
            </Button>
          </div>
        </div>
      </Modal>
    </MainLayout>
  );
}
