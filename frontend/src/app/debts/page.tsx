'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal';
import Input from '@/components/ui/Input';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, CreditCard, User, Search, Eye } from 'lucide-react';
import { debtService } from '@/services/debtService';
import { Debt, CreateDebtRequest, UpdateDebtRequest, PayDebtRequest, PaginationRequest, PaginatedResponse } from '@/types';

export default function DebtsPage() {
  const router = useRouter();
  const [debts, setDebts] = useState<Debt[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isPayModalOpen, setIsPayModalOpen] = useState(false);
  const [editingDebt, setEditingDebt] = useState<Debt | null>(null);
  const [payingDebt, setPayingDebt] = useState<Debt | null>(null);
  const [formData, setFormData] = useState<CreateDebtRequest>({
    name: '',
    surname: '',
    phone: '',
    amount: 0,
  });
  const [payAmount, setPayAmount] = useState(0);
  const [filter, setFilter] = useState<'all' | 'paid' | 'unpaid'>('unpaid');
  const [startDate, setStartDate] = useState('');
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });
  const [endDate, setEndDate] = useState('');

  // Search states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');

  useEffect(() => {
    loadDebts();
  }, [filter]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadDebts = async () => {
    try {
      setLoading(true);
      let data: Debt[] | PaginatedResponse<Debt>;

      // If date range is specified, use date range filter
      if (startDate || endDate) {
        data = await debtService.getByDateRange(startDate, endDate);
        // Apply payment status filter on date-filtered results
        if (filter !== 'all') {
          data = data.filter(debt => filter === 'paid' ? debt.is_paid : !debt.is_paid);
        }
        setDebts(data);
      } else {
        // Use payment status filter with pagination
        switch (filter) {
          case 'paid':
            data = await debtService.getPaid();
            setDebts(data);
            break;
          case 'unpaid':
            data = await debtService.getUnpaid();
            setDebts(data);
            break;
          default:
            data = await debtService.getAll();
            setDebts(data);
        }
      }
    } catch (error) {
      console.error('Error loading debts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Search functions
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setSearchInput('');
    setFilter('unpaid');
    setStartDate('');
    setEndDate('');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleDateFilter = () => {
    // Reset pagination when applying date filter
    setPagination({ page: 1, per_page: pagination.per_page });
    loadDebts();
  };

  const clearDateFilter = () => {
    setStartDate('');
    setEndDate('');
    // Reset pagination when clearing date filter
    setPagination({ page: 1, per_page: pagination.per_page });
    loadDebts();
  };

  const handleCreate = async () => {
    try {
      await debtService.create(formData);
      setIsCreateModalOpen(false);
      setFormData({ name: '', surname: '', phone: '', amount: 0 });
      loadDebts();
    } catch (error) {
      console.error('Error creating debt:', error);
    }
  };

  const handleEdit = async () => {
    if (!editingDebt) return;
    
    try {
      const updateData: UpdateDebtRequest = {
        name: formData.name,
        surname: formData.surname,
        phone: formData.phone,
        amount: formData.amount,
      };
      
      await debtService.update(editingDebt.id, updateData);
      setIsEditModalOpen(false);
      setEditingDebt(null);
      loadDebts();
    } catch (error) {
      console.error('Error updating debt:', error);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu borcu silmek istediğinizden emin misiniz?')) return;
    
    try {
      await debtService.delete(id);
      loadDebts();
    } catch (error) {
      console.error('Error deleting debt:', error);
    }
  };

  const handlePay = async () => {
    if (!payingDebt) return;
    
    try {
      const payData: PayDebtRequest = { amount: payAmount };
      await debtService.pay(payingDebt.id, payData);
      setIsPayModalOpen(false);
      setPayingDebt(null);
      setPayAmount(0);
      loadDebts();
    } catch (error) {
      console.error('Error paying debt:', error);
    }
  };

  const openEditModal = (debt: Debt) => {
    setEditingDebt(debt);
    setFormData({
      name: debt.name,
      surname: debt.surname,
      phone: debt.phone,
      amount: debt.amount,
    });
    setIsEditModalOpen(true);
  };

  const openPayModal = (debt: Debt) => {
    setPayingDebt(debt);
    setPayAmount(debt.amount);
    setIsPayModalOpen(true);
  };

  const handleFilterChange = (newFilter: 'all' | 'paid' | 'unpaid') => {
    setFilter(newFilter);
    // Reset pagination when changing filter
    setPagination({ page: 1, per_page: pagination.per_page });
  };

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount) + ' TL';
  };

  // Normalize Turkish characters for search
  const normalizeTurkish = (text: string) => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c');
  };

  // Filter debts based on search term
  const filteredDebts = debts.filter(debt => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeTurkish(searchTerm);
    return (
      normalizeTurkish(debt.name).includes(normalizedSearchTerm) ||
      normalizeTurkish(debt.surname).includes(normalizedSearchTerm) ||
      normalizeTurkish(debt.phone).includes(normalizedSearchTerm)
    );
  });

  const columns: Column<Debt>[] = [
    {
      key: 'customer',
      header: 'Müşteri',
      render: (debt) => (
        <div className="flex items-center">
          <User className="h-5 w-5 text-blue-600 mr-2" />
          <div>
            <div className="font-medium text-gray-900">{debt.name} {debt.surname}</div>
            <div className="text-sm text-gray-500">{debt.phone}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'amount',
      header: 'Tutar',
      render: (debt) => (
        <div className="flex items-center">
          <CreditCard className="h-4 w-4 text-green-600 mr-1" />
          <span className="font-medium">{formatPrice(debt.amount)}</span>
        </div>
      ),
    },
    {
      key: 'status',
      header: 'Durum',
      render: (debt) => (
        <span className={`px-2 py-1 rounded-full text-xs ${
          debt.is_paid
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {debt.is_paid ? 'Ödendi' : 'Ödenmedi'}
        </span>
      ),
    },
    {
      key: 'date',
      header: 'Tarih',
      render: (debt) => (
        <div className="text-sm text-gray-500">
          {new Date(debt.created_at).toLocaleDateString('tr-TR')}
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'İşlemler',
      className: 'text-right',
      render: (debt) => (
        <div className="flex justify-end space-x-2">
          {!debt.is_paid && (
            <Button
              size="sm"
              variant="warning"
              onClick={() => openPayModal(debt)}
              title="Ödeme Yap"
            >
              <CreditCard className="h-4 w-4" />
            </Button>
          )}
          <Button
            size="sm"
            variant="success"
            onClick={() => router.push(`/debts/${debt.id}`)}
            title="Detay"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="info"
            onClick={() => openEditModal(debt)}
            title="Düzenle"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => handleDelete(debt.id)}
            title="Sil"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Borçlar</h1>
            <p className="text-gray-600">Müşteri borçlarını takip edin</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Borç
            </Button>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
          <button
            onClick={() => handleFilterChange('all')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              filter === 'all'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Tümü
          </button>
          <button
            onClick={() => handleFilterChange('unpaid')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              filter === 'unpaid'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Ödenmemiş
          </button>
          <button
            onClick={() => handleFilterChange('paid')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              filter === 'paid'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Ödenmiş
          </button>
        </div>

        {/* Date Range Filter */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium">Tarih Aralığı Filtresi</h3>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Başlangıç Tarihi
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bitiş Tarihi
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="flex space-x-2 pt-6">
                <Button onClick={handleDateFilter} variant="primary">
                  Filtrele
                </Button>
                <Button onClick={clearDateFilter} variant="secondary">
                  Temizle
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search and Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Müşteri adı, soyad veya telefon ara..."
                value={searchInput}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <Button
              onClick={handleSearch}
              variant="primary"
              className="px-4 py-2"
            >
              Ara
            </Button>
            {(searchTerm || filter !== 'unpaid' || startDate || endDate) && (
              <Button
                onClick={handleClearSearch}
                variant="secondary"
                className="px-4 py-2"
              >
                Temizle
              </Button>
            )}
          </div>

          {/* Search Summary */}
          {searchTerm && (
            <div className="mt-4 text-sm text-gray-600">
              {filteredDebts.length} borç gösteriliyor • &quot;{searchTerm}&quot; araması
            </div>
          )}
        </div>

        {/* Debts Display */}
        <DataTable
          columns={columns}
          data={filteredDebts}
          pagination={{ page: pagination.page, per_page: pagination.per_page, total: filteredDebts.length, total_pages: Math.ceil(filteredDebts.length / pagination.per_page), has_next: pagination.page < Math.ceil(filteredDebts.length / pagination.per_page), has_prev: pagination.page > 1 }}
          onPageChange={handlePageChange}
          onPerPageChange={handlePerPageChange}
          loading={loading}
          emptyMessage="Henüz borç kaydı bulunmuyor"
          emptyIcon={<CreditCard className="h-12 w-12 text-gray-400" />}
          useClientPagination={true}
        />

        {/* Create Modal */}
        <Modal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          title="Yeni Borç Ekle"
        >
          <div className="space-y-4">
            <Input
              label="Ad"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Müşteri adı"
            />
            <Input
              label="Soyad"
              value={formData.surname}
              onChange={(e) => setFormData({ ...formData, surname: e.target.value })}
              placeholder="Müşteri soyadı"
            />
            <Input
              label="Telefon"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="0555 123 4567"
            />
            <Input
              label="Borç Tutarı"
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
              placeholder="0.00"
            />
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsCreateModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleCreate}>
                Borç Ekle
              </Button>
            </div>
          </div>
        </Modal>

        {/* Edit Modal */}
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title="Borç Düzenle"
        >
          <div className="space-y-4">
            <Input
              label="Ad"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Müşteri adı"
            />
            <Input
              label="Soyad"
              value={formData.surname}
              onChange={(e) => setFormData({ ...formData, surname: e.target.value })}
              placeholder="Müşteri soyadı"
            />
            <Input
              label="Telefon"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="0555 123 4567"
            />
            <Input
              label="Borç Tutarı"
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData({ ...formData, amount: parseFloat(e.target.value) || 0 })}
              placeholder="0.00"
            />
            <div className="flex justify-end space-x-3 pt-4">
              <Button variant="secondary" onClick={() => setIsEditModalOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleEdit}>
                Güncelle
              </Button>
            </div>
          </div>
        </Modal>

        {/* Pay Modal */}
        <Modal
          isOpen={isPayModalOpen}
          onClose={() => setIsPayModalOpen(false)}
          title="Borç Öde"
        >
          <div className="space-y-4">
            {payingDebt && (
              <>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900">{payingDebt.name} {payingDebt.surname}</h4>
                  <p className="text-sm text-gray-600">Toplam Borç: ₺{payingDebt.amount}</p>
                </div>
                <Input
                  label="Ödeme Tutarı"
                  type="number"
                  value={payAmount}
                  onChange={(e) => setPayAmount(parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  helperText={`Maksimum: ₺${payingDebt.amount}`}
                />
                <div className="flex justify-end space-x-3 pt-4">
                  <Button variant="secondary" onClick={() => setIsPayModalOpen(false)}>
                    İptal
                  </Button>
                  <Button variant="success" onClick={handlePay}>
                    Ödeme Yap
                  </Button>
                </div>
              </>
            )}
          </div>
        </Modal>
      </div>
    </MainLayout>
  );
}
