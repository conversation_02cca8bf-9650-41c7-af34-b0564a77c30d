@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #4B5563;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Ensure good contrast for form elements */
input, select, textarea {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

input::placeholder {
  color: #6b7280 !important;
}

/* Dark text for labels and form text */
label {
  color: #374151 !important;
}

/* Ensure select options are readable */
option {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 0.375rem;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 0.375rem;
}

.hover\:scrollbar-thumb-gray-400:hover::-webkit-scrollbar-thumb {
  background-color: #9ca3af;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 0.375rem;
}

::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 0.375rem;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
