'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { BarChart3, Package, CreditCard, Vault, Users, Download } from 'lucide-react';
import { dashboardService, DashboardStats, RecentActivity } from '@/services/dashboardService';
import { MonthlySales } from '@/types';
import DateFilter from '@/components/DateFilter';
import TopSellingProducts from '@/components/TopSellingProducts';
import CategorySalesChart from '@/components/CategorySalesChart';
import { reportService } from '@/services/reportService';

export default function ReportsPage() {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalDebts: 0,
    totalSafeAmount: 0,
    unpaidDebtsAmount: 0,
    totalCategories: 0,
    activeCampaigns: 0,
  });
  // const [topProducts, setTopProducts] = useState<Product[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [monthlySales, setMonthlySales] = useState<MonthlySales[]>([]);

  // Date filter state
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const [selectedYear, setSelectedYear] = useState(currentYear);
  const [selectedMonth, setSelectedMonth] = useState(currentMonth);

  useEffect(() => {
    const fetchReportsData = async () => {
      try {
        setLoading(true);
        const [dashboardStats, , activities, monthlyData] = await Promise.all([
          dashboardService.getStats(),
          dashboardService.getTopSellingProducts(5),
          dashboardService.getRecentActivities(5),
          reportService.getMonthlySales(selectedYear),
        ]);

        setStats(dashboardStats);
        // setTopProducts(topSellingProducts);
        setRecentActivities(activities);
        setMonthlySales(monthlyData);
      } catch (error) {
        console.error('Error fetching reports data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchReportsData();
  }, [selectedYear]);

  const handleDateChange = (year: number, month: number) => {
    setSelectedYear(year);
    setSelectedMonth(month);
  };

  const handleExportMonthlySales = async () => {
    try {
      setLoading(true);
      await reportService.exportMonthlySales(selectedYear, selectedMonth);
    } catch (error) {
      console.error('Export error:', error);
      alert('Excel dosyası indirilemedi: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Raporlar</h1>
          <p className="text-gray-600">İşletmenizin performans raporlarını görüntüleyin</p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Package className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Toplam Ürün</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
                  <p className="text-xs text-blue-600 flex items-center">
                    <Package className="h-3 w-3 mr-1" />
                    {stats.totalCategories} kategori
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCard className="h-8 w-8 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Toplam Borç</p>
                  <p className="text-2xl font-bold text-gray-900">₺{stats.unpaidDebtsAmount.toLocaleString()}</p>
                  <p className="text-xs text-red-600 flex items-center">
                    <CreditCard className="h-3 w-3 mr-1" />
                    Ödenmemiş borçlar
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Vault className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Kasa Bakiyesi</p>
                  <p className="text-2xl font-bold text-gray-900">₺{stats.totalSafeAmount.toLocaleString()}</p>
                  <p className="text-xs text-green-600 flex items-center">
                    <Vault className="h-3 w-3 mr-1" />
                    Toplam nakit
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Aktif Kampanya</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.activeCampaigns}</p>
                  <p className="text-xs text-purple-600 flex items-center">
                    <Users className="h-3 w-3 mr-1" />
                    Devam eden
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Date Filter */}
        <DateFilter onDateChange={handleDateChange} />

        {/* Top Selling Products */}
        <TopSellingProducts
          year={selectedYear}
          month={selectedMonth}
          limit={5}
        />

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Sales Trend */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <BarChart3 className="h-5 w-5 text-blue-600 mr-2" />
                  <h3 className="text-lg font-medium text-gray-900">Aylık Satış Trendi</h3>
                </div>
                <Button
                  onClick={handleExportMonthlySales}
                  disabled={loading || monthlySales.length === 0}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 text-white px-3 py-2 text-sm"
                >
                  <Download className="h-4 w-4" />
                  <span>{selectedMonth}/{selectedYear} Excel İndir</span>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {monthlySales.length > 0 ? (
                <div className="space-y-4">
                  {monthlySales.slice(0, 6).map((sale) => {
                    const maxRevenue = Math.max(...monthlySales.map(s => s.total_revenue), 1);
                    const percentage = (sale.total_revenue / maxRevenue) * 100;

                    return (
                      <div key={`${sale.year}-${sale.month}`} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-900">
                            {sale.month}/{sale.year}
                          </span>
                          <div className="flex items-center space-x-4 text-sm">
                            <span className="text-gray-600">{sale.total_sales} adet</span>
                            <span className="text-green-600 font-medium">{sale.total_revenue.toFixed(2)} TL</span>
                          </div>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Satış verisi bulunamadı</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Category Sales */}
          <CategorySalesChart year={selectedYear} month={selectedMonth} />
        </div>

        {/* Additional Reports Section */}
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium text-gray-900">Son İşlemler</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentActivities.length > 0 ? (
                  recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between py-2">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                      <div className="text-right">
                        {activity.amount && (
                          <p className={`text-sm font-medium ${
                            activity.amount.startsWith('+') ? 'text-green-600' :
                            activity.amount.startsWith('-') ? 'text-red-600' : 'text-gray-900'
                          }`}>
                            {activity.amount}
                          </p>
                        )}
                        <p className="text-xs text-gray-500 capitalize">{activity.type}</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">Henüz işlem bulunmuyor</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Financial Summary */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-medium text-gray-900">Mali Özet</h3>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-1">Toplam Kasa</p>
                <p className="text-2xl font-bold text-green-600">₺{stats.totalSafeAmount.toLocaleString()}</p>
                <p className="text-xs text-green-600">Mevcut nakit</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-1">Toplam Borç</p>
                <p className="text-2xl font-bold text-red-600">₺{stats.unpaidDebtsAmount.toLocaleString()}</p>
                <p className="text-xs text-red-600">Ödenmemiş borçlar</p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-500 mb-1">Net Durum</p>
                <p className={`text-2xl font-bold ${
                  (stats.totalSafeAmount - stats.unpaidDebtsAmount) >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  ₺{(stats.totalSafeAmount - stats.unpaidDebtsAmount).toLocaleString()}
                </p>
                <p className="text-xs text-blue-600">Kasa - Borç</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
