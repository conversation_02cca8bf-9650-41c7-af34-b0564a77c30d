'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/layout/AdminLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Modal from '@/components/ui/Modal';
import DataTable, { Column } from '@/components/ui/DataTable';
import { Plus, Edit, Trash2, Search, Eye, Shield, User as UserIcon } from 'lucide-react';
import { User, Organization, CreateUserRequest, UpdateUserRequest, PaginationRequest } from '@/types';
import { authService } from '@/services/authService';
import { organizationService } from '@/services/organizationService';

export default function UsersPage() {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [pagination, setPagination] = useState<PaginationRequest>({ page: 1, per_page: 10 });

  // Search states
  const [searchTerm, setSearchTerm] = useState('');
  const [searchInput, setSearchInput] = useState('');

  // Form states
  const [formData, setFormData] = useState<CreateUserRequest>({
    username: '',
    password: '',
    role: 'user',
    organization_id: '',
  });

  const [updateFormData, setUpdateFormData] = useState<UpdateUserRequest>({
    username: '',
    password: '',
    role: 'user',
    is_active: true,
    organization_id: '',
  });

  useEffect(() => {
    loadUsers();
    loadOrganizations();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const data = await authService.getAllUsers();
      setUsers(data);
    } catch (error) {
      console.error('Error loading users:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadOrganizations = async () => {
    try {
      const data = await organizationService.getAllOrganizations();
      setOrganizations(data);
    } catch (error) {
      console.error('Error loading organizations:', error);
    }
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const handlePerPageChange = (per_page: number) => {
    setPagination({ page: 1, per_page });
  };

  // Search functions
  const handleSearch = () => {
    setSearchTerm(searchInput);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearchInputChange = (value: string) => {
    setSearchInput(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setSearchInput('');
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreate = async () => {
    try {
      await authService.createUser(formData);
      setIsCreateModalOpen(false);
      setFormData({ username: '', password: '', role: 'user', organization_id: '' });
      loadUsers();
    } catch (error) {
      console.error('Error creating user:', error);
      alert('Kullanıcı oluşturulurken hata oluştu: ' + (error as Error).message);
    }
  };

  const openEditModal = (user: User) => {
    setEditingUser(user);
    setUpdateFormData({
      username: user.username,
      password: '',
      role: user.role,
      is_active: user.is_active,
      organization_id: user.organization_id,
    });
    setIsEditModalOpen(true);
  };

  const handleUpdate = async () => {
    if (!editingUser) return;

    try {
      // Remove empty password from update data
      const updateData = { ...updateFormData };
      if (!updateData.password) {
        delete updateData.password;
      }

      await authService.updateUser(editingUser.id, updateData);
      setIsEditModalOpen(false);
      setEditingUser(null);
      setUpdateFormData({ username: '', password: '', role: 'user', is_active: true, organization_id: '' });
      loadUsers();
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Kullanıcı güncellenirken hata oluştu: ' + (error as Error).message);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz?')) return;

    try {
      await authService.deleteUser(id);
      loadUsers();
    } catch (error) {
      console.error('Error deleting user:', error);
      alert('Kullanıcı silinirken hata oluştu: ' + (error as Error).message);
    }
  };

  const handleViewDetails = (id: string) => {
    router.push(`/admin/users/${id}`);
  };

  // Normalize Turkish characters for search
  const normalizeTurkish = (text: string) => {
    return text
      .toLowerCase()
      .replace(/ğ/g, 'g')
      .replace(/ü/g, 'u')
      .replace(/ş/g, 's')
      .replace(/ı/g, 'i')
      .replace(/ö/g, 'o')
      .replace(/ç/g, 'c');
  };

  // Filter users based on search term
  const filteredUsers = users.filter(user => {
    if (!searchTerm) return true;

    const normalizedSearchTerm = normalizeTurkish(searchTerm);
    return (
      normalizeTurkish(user.username).includes(normalizedSearchTerm) ||
      normalizeTurkish(user.role).includes(normalizedSearchTerm)
    );
  });

  // Paginate filtered users
  const startIndex = (pagination.page - 1) * pagination.per_page;
  const endIndex = startIndex + pagination.per_page;
  const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
  const totalPages = Math.ceil(filteredUsers.length / pagination.per_page);

  const columns: Column<User>[] = [
    {
      key: 'username',
      header: 'Kullanıcı Adı',
      render: (user) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-8 w-8">
            <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
              user.role === 'admin' ? 'bg-purple-100' : 'bg-blue-100'
            }`}>
              {user.role === 'admin' ? (
                <Shield className={`h-4 w-4 ${user.role === 'admin' ? 'text-purple-600' : 'text-blue-600'}`} />
              ) : (
                <UserIcon className="h-4 w-4 text-blue-600" />
              )}
            </div>
          </div>
          <div className="ml-3">
            <div className="text-sm font-medium text-gray-900">{user.username}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'role',
      header: 'Rol',
      render: (user) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          user.role === 'admin' 
            ? 'bg-purple-100 text-purple-800' 
            : 'bg-blue-100 text-blue-800'
        }`}>
          {user.role === 'admin' ? 'Admin' : 'Kullanıcı'}
        </span>
      ),
    },
    {
      key: 'organization',
      header: 'Organizasyon',
      render: (user) => {
        const organization = organizations.find(org => org.id === user.organization_id);
        return (
          <div className="text-sm text-gray-900">
            {organization ? organization.name : 'Bilinmiyor'}
          </div>
        );
      },
    },
    {
      key: 'is_active',
      header: 'Durum',
      render: (user) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          user.is_active 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {user.is_active ? 'Aktif' : 'Pasif'}
        </span>
      ),
    },
    {
      key: 'created_at',
      header: 'Oluşturulma Tarihi',
      render: (user) => user.created_at,
    },
    {
      key: 'actions',
      header: 'İşlemler',
      render: (user) => (
        <div className="flex items-center space-x-2 justify-end">
          <Button
            variant="danger"
            size="sm"
            onClick={() => handleDelete(user.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
          <Button
            variant="info"
            size="sm"
            onClick={() => openEditModal(user)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="success"
            size="sm"
            onClick={() => handleViewDetails(user.id)}
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return (
      <ProtectedRoute requireAdmin>
        <AdminLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Yükleniyor...</div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute requireAdmin>
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Kullanıcı Yönetimi</h1>
              <p className="text-gray-600">Sistem kullanıcılarını yönetin</p>
            </div>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Yeni Kullanıcı
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Kullanıcı adı veya rol ara..."
                  value={searchInput}
                  onChange={(e) => handleSearchInputChange(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <Button
                onClick={handleSearch}
                variant="primary"
                className="px-4 py-2"
              >
                Ara
              </Button>
              {(searchTerm) && (
                <Button
                  onClick={handleClearSearch}
                  variant="secondary"
                  className="px-4 py-2"
                >
                  Temizle
                </Button>
              )}
            </div>

            {/* Search Summary */}
            {searchTerm && (
              <div className="mt-4 text-sm text-gray-600">
                {filteredUsers.length} kullanıcı gösteriliyor • &quot;{searchTerm}&quot; araması
              </div>
            )}
          </div>

          {/* Users Table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <DataTable
              data={paginatedUsers}
              columns={columns}
              pagination={{
                page: pagination.page,
                per_page: pagination.per_page,
                total: filteredUsers.length,
                total_pages: totalPages,
                has_next: pagination.page < totalPages,
                has_prev: pagination.page > 1,
              }}
              onPageChange={handlePageChange}
              onPerPageChange={handlePerPageChange}
            />
          </div>

          {/* Create User Modal */}
          <Modal
            isOpen={isCreateModalOpen}
            onClose={() => setIsCreateModalOpen(false)}
            title="Yeni Kullanıcı Oluştur"
          >
            <div className="space-y-4">
              <Input
                label="Kullanıcı Adı"
                value={formData.username}
                onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                placeholder="Kullanıcı adını girin"
                required
              />
              <Input
                label="Şifre"
                type="password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                placeholder="Şifreyi girin"
                required
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rol
                </label>
                <select
                  value={formData.role}
                  onChange={(e) => setFormData({ ...formData, role: e.target.value as 'admin' | 'user' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="user">Kullanıcı</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Organizasyon *
                </label>
                <select
                  value={formData.organization_id}
                  onChange={(e) => setFormData({ ...formData, organization_id: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Organizasyon seçin</option>
                  {organizations.map((org) => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  onClick={() => setIsCreateModalOpen(false)}
                  variant="secondary"
                >
                  İptal
                </Button>
                <Button onClick={handleCreate} variant="primary">
                  Oluştur
                </Button>
              </div>
            </div>
          </Modal>

          {/* Edit User Modal */}
          <Modal
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            title="Kullanıcı Düzenle"
          >
            <div className="space-y-4">
              <Input
                label="Kullanıcı Adı"
                value={updateFormData.username}
                onChange={(e) => setUpdateFormData({ ...updateFormData, username: e.target.value })}
                placeholder="Kullanıcı adını girin"
                required
              />
              <Input
                label="Yeni Şifre (Boş bırakılırsa değişmez)"
                type="password"
                value={updateFormData.password}
                onChange={(e) => setUpdateFormData({ ...updateFormData, password: e.target.value })}
                placeholder="Yeni şifreyi girin"
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rol
                </label>
                <select
                  value={updateFormData.role}
                  onChange={(e) => setUpdateFormData({ ...updateFormData, role: e.target.value as 'admin' | 'user' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="user">Kullanıcı</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Organizasyon
                </label>
                <select
                  value={updateFormData.organization_id}
                  onChange={(e) => setUpdateFormData({ ...updateFormData, organization_id: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Organizasyon seçin</option>
                  {organizations.map((org) => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  checked={updateFormData.is_active}
                  onChange={(e) => setUpdateFormData({ ...updateFormData, is_active: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                  Aktif kullanıcı
                </label>
              </div>
              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  onClick={() => setIsEditModalOpen(false)}
                  variant="secondary"
                >
                  İptal
                </Button>
                <Button onClick={handleUpdate} variant="primary">
                  Güncelle
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
