'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import MainLayout from '@/components/layout/MainLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Package, CreditCard, Vault, TrendingUp } from 'lucide-react';
import { dashboardService, DashboardStats } from '@/services/dashboardService';
import { Product, Debt } from '@/types';

export default function Home() {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalDebts: 0,
    totalSafeAmount: 0,
    unpaidDebtsAmount: 0,
    totalCategories: 0,
    activeCampaigns: 0,
  });
  const [recentProducts, setRecentProducts] = useState<Product[]>([]);
  const [recentDebts, setRecentDebts] = useState<Debt[]>([]);

  // Redirect admin users to admin panel
  useEffect(() => {
    if (isAdmin) {
      router.push('/admin');
      return;
    }
  }, [isAdmin, router]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const [dashboardStats, products, debts] = await Promise.all([
          dashboardService.getStats(),
          dashboardService.getRecentProducts(3),
          dashboardService.getRecentDebts(3),
        ]);

        setStats(dashboardStats);
        setRecentProducts(products);
        setRecentDebts(debts);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <ProtectedRoute>
        <MainLayout>
          <div className="flex items-center justify-center h-64">
            <div className="text-lg">Yükleniyor...</div>
          </div>
        </MainLayout>
      </ProtectedRoute>
    );
  }

  // Don't render for admin users (they will be redirected)
  if (isAdmin) {
    return null;
  }

  return (
    <ProtectedRoute>
      <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">İşletme yönetim sisteminize hoş geldiniz</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Package className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Toplam Ürün</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CreditCard className="h-8 w-8 text-red-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Toplam Borç</p>
                  <p className="text-2xl font-bold text-gray-900">₺{stats.totalDebts.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Vault className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Kasa Bakiyesi</p>
                  <p className="text-2xl font-bold text-gray-900">₺{stats.totalSafeAmount.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Net Bakiye</p>
                  <p className="text-2xl font-bold text-gray-900">₺{(stats.totalSafeAmount - stats.unpaidDebtsAmount).toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium text-gray-900">Son Ürünler</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentProducts.length > 0 ? (
                  recentProducts.map((product) => (
                    <div key={product.id} className="flex items-center justify-between py-2">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{product.name}</p>
                        <p className="text-xs text-gray-500">Kod: {product.product_code}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">₺{product.price.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">{product.quantity} adet</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">Henüz ürün bulunmuyor</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <h3 className="text-lg font-medium text-gray-900">Son Borçlar</h3>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentDebts.length > 0 ? (
                  recentDebts.map((debt) => (
                    <div key={debt.id} className="flex items-center justify-between py-2">
                      <div>
                        <p className="text-sm font-medium text-gray-900">{debt.name} {debt.surname}</p>
                        <p className="text-xs text-gray-500">{debt.phone}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-red-600">₺{debt.amount.toLocaleString()}</p>
                        <p className="text-xs text-gray-500">
                          {debt.is_paid ? 'Ödendi' : 'Ödenmemiş'}
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">Henüz borç bulunmuyor</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </MainLayout>
    </ProtectedRoute>
  );
}
