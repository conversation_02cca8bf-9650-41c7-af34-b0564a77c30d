// import { apiClient } from '@/lib/api';
import { productService } from './productService';
import { debtService } from './debtService';
import { safeService } from './safeService';
import { categoryService } from './categoryService';
import { campaignService } from './campaignService';
import { Product, Debt } from '@/types';

export interface DashboardStats {
  totalProducts: number;
  totalDebts: number;
  totalSafeAmount: number;
  unpaidDebtsAmount: number;
  totalCategories: number;
  activeCampaigns: number;
}

export interface RecentActivity {
  id: string;
  type: 'product' | 'debt' | 'safe' | 'category' | 'campaign';
  description: string;
  amount?: string;
  time: string;
}

export const dashboardService = {
  // Get dashboard statistics
  async getStats(): Promise<DashboardStats> {
    try {
      const [products, debts, safes, categories, campaigns] = await Promise.all([
        productService.getAll(),
        debtService.getAll(),
        safeService.getAll(),
        categoryService.getAll(),
        campaignService.getAll(),
      ]);

      // Calculate total safe amount
      const totalSafeAmount = safes.reduce((total, safe) => total + safe.amount, 0);

      // Calculate unpaid debts amount
      const unpaidDebts = debts.filter(debt => !debt.is_paid);
      const unpaidDebtsAmount = unpaidDebts.reduce((total, debt) => total + debt.amount, 0);

      // Count active campaigns (campaigns that haven't ended yet)
      const now = new Date();
      const activeCampaigns = campaigns.filter(campaign => {
        const endDate = new Date(campaign.end_date);
        return endDate > now;
      }).length;

      return {
        totalProducts: products.length,
        totalDebts: unpaidDebtsAmount,
        totalSafeAmount,
        unpaidDebtsAmount,
        totalCategories: categories.length,
        activeCampaigns,
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        totalProducts: 0,
        totalDebts: 0,
        totalSafeAmount: 0,
        unpaidDebtsAmount: 0,
        totalCategories: 0,
        activeCampaigns: 0,
      };
    }
  },

  // Get recent products
  async getRecentProducts(limit: number = 5): Promise<Product[]> {
    try {
      const products = await productService.getAll();
      return products
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching recent products:', error);
      return [];
    }
  },

  // Get recent debts
  async getRecentDebts(limit: number = 5): Promise<Debt[]> {
    try {
      const debts = await debtService.getUnpaid();
      return debts
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching recent debts:', error);
      return [];
    }
  },

  // Get top selling products (mock for now, can be enhanced with sales data)
  async getTopSellingProducts(limit: number = 5): Promise<Product[]> {
    try {
      const products = await productService.getAll();
      // For now, sort by quantity (assuming higher quantity means more popular)
      return products
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching top selling products:', error);
      return [];
    }
  },

  // Get recent activities (mock implementation)
  async getRecentActivities(limit: number = 5): Promise<RecentActivity[]> {
    try {
      const [products, debts] = await Promise.all([
        this.getRecentProducts(3),
        this.getRecentDebts(2),
      ]);

      const activities: RecentActivity[] = [];

      // Add recent products
      products.forEach(product => {
        activities.push({
          id: product.id,
          type: 'product',
          description: `Yeni ürün eklendi: ${product.name}`,
          amount: `₺${product.price}`,
          time: this.formatTimeAgo(product.created_at),
        });
      });

      // Add recent debts
      debts.forEach(debt => {
        activities.push({
          id: debt.id,
          type: 'debt',
          description: `Yeni borç kaydı: ${debt.name} ${debt.surname}`,
          amount: `₺${debt.amount}`,
          time: this.formatTimeAgo(debt.created_at),
        });
      });

      // Sort by creation time and limit
      return activities
        .sort(() => {
          // This is a simplified sort, in real implementation you'd parse the time strings
          return Math.random() - 0.5; // Random for now
        })
        .slice(0, limit);
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      return [];
    }
  },

  // Helper function to format time ago
  formatTimeAgo(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Az önce';
    } else if (diffInHours < 24) {
      return `${diffInHours} saat önce`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} gün önce`;
    }
  },
};
