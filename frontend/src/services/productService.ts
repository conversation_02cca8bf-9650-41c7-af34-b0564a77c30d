import { apiClient } from '@/lib/api';
import {
  Product,
  CreateProductRequest,
  UpdateProductRequest,
  ApiResponse,
  PaginatedResponse,
  PaginationRequest,
  ExcelImportRequest,
  ExcelImportResponse
} from '@/types';

export const productService = {
  // Get all products
  async getAll(): Promise<Product[]> {
    const response = await apiClient.get<ApiResponse<Product[]>>('/products');
    return response.data || [];
  },

  // Get products with pagination
  async getPaginated(pagination: PaginationRequest): Promise<PaginatedResponse<Product>> {
    const queryParams = new URLSearchParams({
      page: pagination.page.toString(),
      per_page: pagination.per_page.toString(),
    });
    const response = await apiClient.get<PaginatedResponse<Product>>(`/products/paginated?${queryParams}`);
    return response;
  },

  // Get product by ID
  async getById(id: string): Promise<Product | null> {
    try {
      const response = await apiClient.get<ApiResponse<Product>>(`/products/${id}`);
      return response.data || null;
    } catch (error) {
      console.error('Error fetching product:', error);
      return null;
    }
  },

  // Create new product
  async create(data: CreateProductRequest): Promise<void> {
    // Handle empty campaign_id - convert to null UUID if empty
    const productData = {
      ...data,
      campaign_id: data.campaign_id || '00000000-0000-0000-0000-000000000000'
    };
    await apiClient.post<ApiResponse<void>>('/products', productData);
  },

  // Update product
  async update(id: string, data: UpdateProductRequest): Promise<void> {
    // Handle empty campaign_id - convert to null UUID if empty
    const productData = {
      ...data,
      campaign_id: data.campaign_id || '00000000-0000-0000-0000-000000000000'
    };
    await apiClient.put<ApiResponse<void>>(`/products/${id}`, productData);
  },

  // Delete product
  async delete(id: string): Promise<void> {
    await apiClient.delete<ApiResponse<void>>(`/products/${id}`);
  },

  // Search products
  async search(query: string): Promise<Product[]> {
    const response = await apiClient.get<ApiResponse<Product[]>>(`/products/search?q=${encodeURIComponent(query)}`);
    return response.data || [];
  },

  // Import products from Excel
  async importFromExcel(data: ExcelImportRequest): Promise<ExcelImportResponse> {
    const response = await apiClient.post<ApiResponse<ExcelImportResponse>>('/products/import-excel', data);
    return response.data!;
  },
};
