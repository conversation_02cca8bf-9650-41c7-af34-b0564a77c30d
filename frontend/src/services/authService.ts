import { apiClient } from '@/lib/api';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  LoginRequest,
  LoginResponse,
  ApiResponse,
  PaginatedResponse,
  PaginationRequest
} from '@/types';

export const authService = {
  // Authentication
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await apiClient.post<ApiResponse<LoginResponse>>('/auth/login', credentials);
    if (!response.data) {
      throw new Error('Login failed: No data received');
    }
    return response.data;
  },

  // User Management (Admin only)
  async createUser(userData: CreateUserRequest): Promise<void> {
    await apiClient.post<ApiResponse<void>>('/users', userData);
  },

  async getAllUsers(): Promise<User[]> {
    const response = await apiClient.get<ApiResponse<User[]>>('/users');
    return response.data || [];
  },

  async getUsersPaginated(pagination: PaginationRequest): Promise<PaginatedResponse<User>> {
    const queryParams = new URLSearchParams({
      page: pagination.page.toString(),
      per_page: pagination.per_page.toString(),
    });
    const response = await apiClient.get<PaginatedResponse<User>>(`/users/paginated?${queryParams}`);
    return response;
  },

  async getUserById(id: string): Promise<User> {
    const response = await apiClient.get<ApiResponse<User>>(`/users/${id}`);
    if (!response.data) {
      throw new Error('User not found');
    }
    return response.data;
  },

  async updateUser(id: string, userData: UpdateUserRequest): Promise<void> {
    await apiClient.put<ApiResponse<void>>(`/users/${id}`, userData);
  },

  async deleteUser(id: string): Promise<void> {
    await apiClient.delete<ApiResponse<void>>(`/users/${id}`);
  },
};
