/**
 * Utility functions for the application
 */

/**
 * Format date string to Turkish locale format
 * @param dateString - Date string in ISO format or any valid date format
 * @returns Formatted date string in Turkish locale
 */
export function formatDate(dateString: string): string {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return dateString; // Return original string if invalid
    }
    
    // Format as Turkish locale date and time
    return date.toLocaleString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString; // Return original string on error
  }
}

/**
 * Format currency to Turkish Lira format
 * @param amount - Amount to format
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number): string {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return '₺0,00';
  }
  
  return `₺${amount.toLocaleString('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`;
}

/**
 * Format phone number to Turkish format
 * @param phone - Phone number string
 * @returns Formatted phone number
 */
export function formatPhone(phone: string): string {
  if (!phone) return '';
  
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');
  
  // Format Turkish mobile number (0XXX XXX XX XX)
  if (digits.length === 11 && digits.startsWith('0')) {
    return `${digits.slice(0, 4)} ${digits.slice(4, 7)} ${digits.slice(7, 9)} ${digits.slice(9, 11)}`;
  }
  
  // Format Turkish mobile number without leading 0 (XXX XXX XX XX)
  if (digits.length === 10) {
    return `0${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 8)} ${digits.slice(8, 10)}`;
  }
  
  return phone; // Return original if doesn't match expected format
}

/**
 * Format Turkish ID number (TC Kimlik)
 * @param tc - TC number string
 * @returns Formatted TC number
 */
export function formatTC(tc: string): string {
  if (!tc) return '';
  
  // Remove all non-digit characters
  const digits = tc.replace(/\D/g, '');
  
  // Format Turkish ID (XXX XXX XXX XX)
  if (digits.length === 11) {
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)} ${digits.slice(9, 11)}`;
  }
  
  return tc; // Return original if doesn't match expected format
}

/**
 * Truncate text to specified length
 * @param text - Text to truncate
 * @param maxLength - Maximum length
 * @returns Truncated text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) {
    return text;
  }
  
  return text.slice(0, maxLength) + '...';
}

/**
 * Check if a string is a valid email
 * @param email - Email string to validate
 * @returns Boolean indicating if email is valid
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Check if a string is a valid Turkish phone number
 * @param phone - Phone number to validate
 * @returns Boolean indicating if phone is valid
 */
export function isValidPhone(phone: string): boolean {
  const digits = phone.replace(/\D/g, '');
  return digits.length === 10 || (digits.length === 11 && digits.startsWith('0'));
}

/**
 * Check if a string is a valid Turkish ID number (TC Kimlik)
 * @param tc - TC number to validate
 * @returns Boolean indicating if TC is valid
 */
export function isValidTC(tc: string): boolean {
  const digits = tc.replace(/\D/g, '');
  
  if (digits.length !== 11) {
    return false;
  }
  
  // TC Kimlik validation algorithm
  const tcArray = digits.split('').map(Number);
  
  // First digit cannot be 0
  if (tcArray[0] === 0) {
    return false;
  }
  
  // Calculate check digits
  const oddSum = tcArray[0] + tcArray[2] + tcArray[4] + tcArray[6] + tcArray[8];
  const evenSum = tcArray[1] + tcArray[3] + tcArray[5] + tcArray[7];
  
  const check1 = ((oddSum * 7) - evenSum) % 10;
  const check2 = (oddSum + evenSum + tcArray[9]) % 10;
  
  return check1 === tcArray[9] && check2 === tcArray[10];
}

/**
 * Generate a random ID
 * @param length - Length of the ID
 * @returns Random ID string
 */
export function generateId(length: number = 8): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Debounce function
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Deep clone an object
 * @param obj - Object to clone
 * @returns Cloned object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    Object.keys(obj).forEach(key => {
      (cloned as Record<string, unknown>)[key] = deepClone((obj as Record<string, unknown>)[key]);
    });
    return cloned;
  }
  
  return obj;
}
